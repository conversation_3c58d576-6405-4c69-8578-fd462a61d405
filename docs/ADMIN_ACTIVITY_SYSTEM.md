# Admin Activity System Documentation

## Overview

Hệ thống Admin Activity System cung cấp một bộ công cụ quản lý hoàn chỉnh cho việc monitor, khởi tạo, chỉnh sửa và xóa các hoạt động trong Activity Cashback System. Hệ thống được thiết kế với role-based authentication và comprehensive analytics.

## Architecture

### 1. Admin Service Layer
- **AdminActivityService**: Core service cho admin operations
- **AdminAnalyticsService**: Advanced analytics và reporting
- **AdminAuthService**: Authentication và authorization

### 2. GraphQL API Layer
- **AdminActivityResolver**: GraphQL resolvers cho admin operations
- **Admin Directives**: `@adminAuth` directive cho authentication
- **Permission-based Authorization**: Fine-grained permissions

### 3. Authentication & Authorization

#### Admin Roles
```typescript
enum AdminRole {
  SUPER_ADMIN     // Full access to all features
  MODERATOR       // Limited management access
  ANALYST         // Read-only analytics access
  SUPPORT         // Basic support access
}
```

#### Admin Permissions
```typescript
enum AdminPermission {
  // Dashboard & Analytics
  VIEW_ACTIVITY_DASHBOARD
  VIEW_ACTIVITY_ANALYTICS
  EXPORT_ACTIVITY_DATA
  
  // Tier Management
  MANAGE_ACTIVITY_TIERS
  
  // Task Management
  MANAGE_ACTIVITY_TASKS
  
  // User Management
  MANAGE_USER_ACTIVITY
  ADJUST_USER_POINTS
  FORCE_USER_TIER_UPGRADE
  
  // System Operations
  SYSTEM_OPERATIONS
  VIEW_SYSTEM_HEALTH
}
```

## Admin Features

### 1. Dashboard & Monitoring

#### Admin Dashboard
```graphql
query AdminActivityDashboard {
  adminActivityDashboard {
    totalUsers
    totalActiveTasks
    totalPointsAwarded
    totalCashbackDistributed
    tierDistribution {
      tier { name level }
      userCount
      percentage
    }
    taskCompletionStats {
      task { name type }
      completionCount
      completionRate
    }
    recentActivities {
      userId
      activityType
      description
      pointsAwarded
      timestamp
    }
    systemHealth {
      status
      pendingTasks
      errorCount
      uptime
    }
  }
}
```

#### System Health Monitoring
- Real-time system status
- Pending tasks tracking
- Error monitoring
- Uptime statistics

### 2. Tier Management

#### Create Activity Tier
```graphql
mutation AdminCreateActivityTier($input: CreateActivityTierInput!) {
  adminCreateActivityTier(input: $input) {
    success
    message
    affectedCount
  }
}
```

#### Update Activity Tier
```graphql
mutation AdminUpdateActivityTier($input: UpdateActivityTierInput!) {
  adminUpdateActivityTier(input: $input) {
    success
    message
  }
}
```

#### Delete Activity Tier
```graphql
mutation AdminDeleteActivityTier($id: Int!) {
  adminDeleteActivityTier(id: $id) {
    success
    message
  }
}
```

### 3. Task Management

#### Create Activity Task
```graphql
mutation AdminCreateActivityTask($input: CreateActivityTaskInput!) {
  adminCreateActivityTask(input: $input) {
    success
    message
    affectedCount
  }
}
```

#### Bulk Task Operations
```graphql
mutation AdminBulkTaskOperation($input: BulkTaskOperationInput!) {
  adminBulkTaskOperation(input: $input) {
    success
    message
    affectedCount
    errors
  }
}
```

#### Task Analytics
```graphql
query AdminTaskAnalytics($taskId: ID!) {
  adminTaskAnalytics(taskId: $taskId) {
    task { name type }
    totalCompletions
    uniqueUsers
    averageCompletionTime
    completionsByDay {
      date
      completions
      uniqueUsers
    }
    topUsers {
      user { email }
      completionCount
      totalPointsEarned
    }
  }
}
```

### 4. User Management

#### User Activity Details
```graphql
query AdminUserActivityDetail($userId: ID!) {
  adminUserActivityDetail(userId: $userId) {
    user { email }
    activityLevel {
      activityTier { name level }
      totalPoints
      currentPoints
      totalTradingVolume
    }
    recentTasks {
      task { name }
      status
      currentCount
      pointsEarned
    }
    pointHistory {
      points
      type
      description
      createdAt
    }
    tierHistory {
      fromTier { name }
      toTier { name }
      pointsAtUpgrade
      createdAt
    }
  }
}
```

#### Manual Point Adjustment
```graphql
mutation AdminAdjustUserPoints($input: ManualPointAdjustmentInput!) {
  adminAdjustUserPoints(input: $input) {
    success
    message
  }
}
```

#### Force Tier Upgrade
```graphql
mutation AdminForceUserTierUpgrade($userId: ID!, $tierLevel: Int!) {
  adminForceUserTierUpgrade(userId: $userId, tierLevel: $tierLevel) {
    success
    message
  }
}
```

### 5. System Operations

#### Reset Daily Tasks
```graphql
mutation AdminResetDailyTasks {
  adminResetDailyTasks {
    success
    message
  }
}
```

#### Process Expired Tasks
```graphql
mutation AdminProcessExpiredTasks {
  adminProcessExpiredTasks {
    success
    message
  }
}
```

#### Recalculate User Tiers
```graphql
mutation AdminRecalculateUserTiers {
  adminRecalculateUserTiers {
    success
    message
  }
}
```

### 6. Data Export

#### Export User Activities
```graphql
query AdminExportUserActivities($filter: AdminUserFilterInput) {
  adminExportUserActivities(filter: $filter)
}
```

#### Export Task Completions
```graphql
query AdminExportTaskCompletions($filter: AdminTaskFilterInput) {
  adminExportTaskCompletions(filter: $filter)
}
```

## Implementation Details

### 1. Admin Authentication Setup

```go
// Initialize admin auth service
adminAuthService := middleware.NewAdminAuthService()

// Check admin permissions in resolvers
func (r *AdminActivityResolver) SomeAdminOperation(ctx context.Context) error {
    if !IsAdminUser(ctx) {
        return fmt.Errorf("admin access required")
    }
    
    // Check specific permission
    if !middleware.HasAdminPermission(ctx, middleware.PermissionManageActivityTiers) {
        return fmt.Errorf("insufficient permissions")
    }
    
    // Proceed with operation
    return nil
}
```

### 2. Admin Service Integration

```go
// Create admin service
adminService := activityService.NewAdminActivityService(
    tierService, taskService, integrationService,
    tierRepo, taskRepo, rewardRepo)

// Use in resolver
adminResolver := &AdminActivityResolver{
    adminService: adminService,
    tierService:  tierService,
    taskService:  taskService,
}
```

### 3. Audit Logging

```go
// Log admin actions
middleware.LogAdminAction(ctx, "CREATE_TIER", "activity_tier", 
    &tierID, "Created new Bronze tier", true, nil)
```

## Security Considerations

### 1. Authentication
- JWT-based authentication required
- Admin role verification
- Session management

### 2. Authorization
- Role-based access control (RBAC)
- Permission-based operations
- Resource-level permissions

### 3. Audit Trail
- All admin actions logged
- User identification tracking
- Operation result tracking

### 4. Data Protection
- Sensitive data masking
- Export restrictions
- Access logging

## Usage Examples

### 1. Admin Dashboard Setup
```typescript
// Frontend query for dashboard
const ADMIN_DASHBOARD = gql`
  query AdminDashboard {
    adminActivityDashboard {
      totalUsers
      totalActiveTasks
      totalPointsAwarded
      totalCashbackDistributed
      systemHealth {
        status
        pendingTasks
        uptime
      }
    }
  }
`;
```

### 2. Tier Management
```typescript
// Create new tier
const CREATE_TIER = gql`
  mutation CreateTier($input: CreateActivityTierInput!) {
    adminCreateActivityTier(input: $input) {
      success
      message
    }
  }
`;

const newTier = {
  name: "Platinum",
  level: 4,
  minPoints: 10000,
  maxPoints: 24999,
  color: "#E5E4E2",
  cashbackPercentage: 4.0,
  description: "Platinum tier with 4% cashback"
};
```

### 3. User Management
```typescript
// Adjust user points
const ADJUST_POINTS = gql`
  mutation AdjustPoints($input: ManualPointAdjustmentInput!) {
    adminAdjustUserPoints(input: $input) {
      success
      message
    }
  }
`;

const adjustment = {
  userId: "user-uuid",
  points: 1000,
  reason: "Bonus points for beta testing",
  description: "Rewarding early adopter"
};
```

## Best Practices

### 1. Admin Operations
- Always verify permissions before operations
- Log all administrative actions
- Use transactions for data consistency
- Implement proper error handling

### 2. Data Management
- Regular backups before bulk operations
- Validate input data thoroughly
- Use pagination for large datasets
- Implement rate limiting

### 3. Security
- Regular permission audits
- Monitor admin activities
- Implement IP restrictions if needed
- Use secure communication channels

### 4. Performance
- Cache frequently accessed data
- Use database indexes effectively
- Implement query optimization
- Monitor system resources

## Troubleshooting

### Common Issues
1. **Permission Denied**: Check user role and permissions
2. **Operation Failed**: Verify data integrity and constraints
3. **Export Timeout**: Use filters to limit data size
4. **System Health Issues**: Check background processes

### Monitoring
- System health dashboard
- Error rate monitoring
- Performance metrics
- User activity tracking

## Future Enhancements

### Planned Features
1. **Advanced Analytics**: Machine learning insights
2. **Automated Actions**: Rule-based automation
3. **Multi-tenant Support**: Organization-level admin
4. **API Rate Limiting**: Enhanced security
5. **Real-time Notifications**: Admin alerts system
