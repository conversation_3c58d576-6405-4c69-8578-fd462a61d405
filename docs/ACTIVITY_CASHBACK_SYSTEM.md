# Activity Cashback System

## Overview

The Activity Cashback system is a comprehensive user engagement and reward system that operates independently from the referral commission system. It focuses on user-platform interactions through three main task categories: Daily Tasks, Community Tasks, and Trading Tasks. Users earn points by completing various tasks, which increase their tier level and unlock higher cashback percentages.

## Key Features

### 1. Independent Tier System
- **5 Activity Tiers**: Bronze, Silver, Gold, Platinum, Diamond
- **Point-based Progression**: Users advance through tiers by earning points
- **Cashback Benefits**: Each tier offers different cashback percentages (1% to 5%)
- **Separate from Referral System**: Completely independent tier system for fairness

### 2. Task System
- **Daily Tasks**: Reset daily at UTC 0:00 (login, trading volume, etc.)
- **Community Tasks**: Social media interactions, sharing, etc.
- **Trading Tasks**: Volume-based and transaction-based tasks
- **One-time Tasks**: Welcome tasks, achievements
- **Unlimited Tasks**: Repeatable tasks with immediate rewards
- **Progress Tasks**: Streak-based tasks (consecutive logins, etc.)
- **Manual Update Tasks**: Tasks requiring manual verification

### 3. Points and Rewards
- **Point Earning**: Complete tasks to earn points
- **Automatic Claiming**: Points are automatically claimed upon task completion
- **Tier Upgrades**: Automatic tier upgrades based on point thresholds
- **Cashback Calculation**: Based on collected trading fees and user's tier

## Architecture

### Database Schema

#### Core Tables
- `activity_tiers`: Tier definitions and benefits
- `user_activity_levels`: User tier information and points
- `user_point_transactions`: Point transaction history
- `activity_tier_upgrades`: Tier upgrade history

#### Task System Tables
- `activity_tasks`: Task definitions
- `user_task_progress`: User progress on tasks
- `task_completions`: Task completion history

#### Reward System Tables
- `activity_rewards`: Reward records
- `user_activity_stats`: Aggregated user statistics
- `activity_cashback_claims`: Cashback claim records
- `user_daily_activities`: Daily activity tracking

### Service Layer

#### ActivityTierService
- Manages user tiers and point calculations
- Handles tier upgrades and cashback percentage calculations
- Provides tier distribution analytics

#### ActivityTaskService
- Manages task lifecycle and user progress
- Processes task completions and reward claiming
- Handles daily task resets and expiration

#### ActivityIntegrationService
- Integrates with existing systems (trading, user management)
- Processes real-time activities (login, trading, social)
- Calculates and awards activity cashback

### Repository Layer
- `ActivityTierRepository`: Tier and user level data access
- `ActivityTaskRepository`: Task and progress data access
- `ActivityRewardRepository`: Reward and statistics data access

## API Endpoints (GraphQL)

### Queries
```graphql
# Get all activity tiers
activityTiers: ActivityTierResponse!

# Get user's activity level and tier information
userActivityLevel: UserActivityLevelResponse!

# Get user's tasks with optional filtering
userTasks(input: GetUserTasksInput): UserTasksResponse!

# Get user's activity statistics
userActivityStats: UserActivityStatsResponse!

# Get user's activity rewards
userActivityRewards: ActivityRewardsResponse!
```

### Mutations
```graphql
# Claim reward for a completed task
claimTaskReward(input: ClaimTaskRewardInput!): TaskRewardClaimResponse!

# Refresh user's task list
refreshUserTasks(input: RefreshUserTasksInput): UserTasksResponse!

# Manually complete a task (for manual verification tasks)
completeTask(input: CompleteTaskInput!): TaskRewardClaimResponse!
```

## Tier System

### Tier Levels
1. **Bronze** (0-999 points): 1% cashback
2. **Silver** (1,000-4,999 points): 2% cashback
3. **Gold** (5,000-14,999 points): 3% cashback
4. **Platinum** (15,000-49,999 points): 4% cashback
5. **Diamond** (50,000+ points): 5% cashback

### Tier Benefits
- **Cashback Percentage**: Primary benefit for trading fee rebates
- **Task Access**: Higher tiers unlock exclusive tasks
- **Priority Support**: Enhanced support for higher tiers
- **Special Events**: Access to tier-specific events and competitions

## Task Categories

### Daily Tasks
- **Daily Login**: 10 points
- **Complete 3 Trades**: 50 points
- **Trade $100 Volume**: 75 points
- **Check-in Streak**: Bonus points for consecutive days

### Community Tasks
- **Follow on Twitter**: 100 points (one-time)
- **Share Trading Results**: 25 points
- **Join Discord**: 50 points (one-time)
- **Refer a Friend**: 200 points

### Trading Tasks
- **Weekly Volume Target**: 150 points
- **Monthly Volume Milestone**: 500 points
- **Perfect Trading Day**: 100 points (no losses)
- **High-Volume Trade**: 75 points (single trade >$1000)

## Cashback Calculation

### Formula
```
Cashback Amount = Trading Fee × User Tier Cashback Percentage
```

### Example
- User trades $1,000 worth of MEME tokens
- Trading fee: $1,000 × 0.95% = $9.50
- User is Gold tier (3% cashback)
- Cashback: $9.50 × 3% = $0.285

### Integration Points
- **Trading System**: Automatically processes trading transactions
- **User Management**: Initializes activity system for new users
- **Reward System**: Integrates with existing reward claiming infrastructure

## Background Processing

### Daily Tasks (UTC 0:00)
- Reset daily task progress
- Update daily statistics
- Process streak calculations

### Periodic Tasks
- Process expired tasks
- Calculate tier upgrades
- Update activity statistics
- Process reward claims

## Usage Examples

### Initialize Activity System for New User
```go
integrationService.InitializeUserActivitySystem(ctx, userID)
```

### Process Trading Transaction
```go
integrationService.ProcessTradingTransaction(ctx, transaction)
```

### Process User Login
```go
integrationService.ProcessUserLogin(ctx, userID)
```

### Get User's Cashback Rate
```go
cashbackRate, err := integrationService.GetUserCashbackRate(ctx, userID)
```

## Migration

Run the migration file to set up the database schema:
```sql
-- Run migrations/20250822120000_activity_system.sql
```

## Initialization

Initialize the system with default tiers and tasks:
```go
import "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"

// Initialize activity system
err := initializer.InitActivitySystem()
```

## Monitoring and Analytics

### Key Metrics
- User tier distribution
- Task completion rates
- Average points per user
- Cashback amounts distributed
- User engagement metrics

### Dashboards
- Real-time activity monitoring
- Tier progression analytics
- Task performance metrics
- Revenue impact analysis

## Future Enhancements

### Planned Features
- **Seasonal Events**: Limited-time tasks and bonuses
- **Achievement System**: Long-term goals and milestones
- **Leaderboards**: Competitive elements
- **NFT Rewards**: Special rewards for top performers
- **Mobile App Integration**: Push notifications for task completion

### Scalability Considerations
- **Batch Processing**: For large user bases
- **Caching**: Redis integration for frequently accessed data
- **Event Streaming**: Real-time activity processing
- **Microservices**: Separate activity service deployment

## Security Considerations

- **Point Manipulation**: Validation and audit trails
- **Task Verification**: Manual verification for social tasks
- **Rate Limiting**: Prevent abuse of unlimited tasks
- **Data Integrity**: Consistent state management across systems
