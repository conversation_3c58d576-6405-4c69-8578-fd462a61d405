package activity

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity"
)

// AdminActivityServiceInterface defines the interface for admin activity operations
type AdminActivityServiceInterface interface {
	// Dashboard and Monitoring
	GetDashboardStats(ctx context.Context) (*AdminDashboardStats, error)
	GetSystemHealth(ctx context.Context) (*SystemHealthStats, error)
	GetTierDistribution(ctx context.Context) ([]TierDistributionStats, error)
	GetTaskCompletionStats(ctx context.Context, dateFrom, dateTo time.Time) ([]TaskCompletionStats, error)

	// Tier Management
	CreateActivityTier(ctx context.Context, tier *model.ActivityTier) error
	UpdateActivityTier(ctx context.Context, tier *model.ActivityTier) error
	DeleteActivityTier(ctx context.Context, tierID uint) error
	GetAllTiersWithStats(ctx context.Context) ([]TierWithStats, error)

	// Task Management
	CreateActivityTask(ctx context.Context, task *model.ActivityTask) error
	UpdateActivityTask(ctx context.Context, task *model.ActivityTask) error
	DeleteActivityTask(ctx context.Context, taskID uuid.UUID) error
	BulkUpdateTasks(ctx context.Context, taskIDs []uuid.UUID, operation string, newStatus *model.TaskStatus) error
	GetTaskAnalytics(ctx context.Context, taskID uuid.UUID) (*TaskAnalytics, error)

	// User Management
	GetUserActivityDetails(ctx context.Context, userID uuid.UUID) (*UserActivityDetails, error)
	AdjustUserPoints(ctx context.Context, userID uuid.UUID, points int, reason, description string) error
	ResetUserTaskProgress(ctx context.Context, userID, taskID uuid.UUID) error
	ForceUserTierUpgrade(ctx context.Context, userID uuid.UUID, tierLevel int) error
	GetUsersWithFilters(ctx context.Context, filters *AdminUserFilters) ([]UserActivityDetails, int, error)

	// System Operations
	ResetAllDailyTasks(ctx context.Context) error
	ProcessAllExpiredTasks(ctx context.Context) error
	RecalculateAllUserTiers(ctx context.Context) error
	InitializeUserActivity(ctx context.Context, userID uuid.UUID) error
	BulkInitializeUsers(ctx context.Context, userIDs []uuid.UUID) error

	// Analytics and Reporting
	ExportUserActivities(ctx context.Context, filters *AdminUserFilters) (string, error)
	ExportTaskCompletions(ctx context.Context, filters *AdminTaskFilters) (string, error)
	GetRecentActivities(ctx context.Context, limit int) ([]RecentActivityRecord, error)
}

// AdminActivityService implements the admin activity service interface
type AdminActivityService struct {
	tierService        ActivityTierServiceInterface
	taskService        ActivityTaskServiceInterface
	integrationService *ActivityIntegrationService
	tierRepo           activity.ActivityTierRepositoryInterface
	taskRepo           activity.ActivityTaskRepositoryInterface
	rewardRepo         activity.ActivityRewardRepositoryInterface
}

// NewAdminActivityService creates a new admin activity service
func NewAdminActivityService(
	tierService ActivityTierServiceInterface,
	taskService ActivityTaskServiceInterface,
	integrationService *ActivityIntegrationService,
	tierRepo activity.ActivityTierRepositoryInterface,
	taskRepo activity.ActivityTaskRepositoryInterface,
	rewardRepo activity.ActivityRewardRepositoryInterface,
) AdminActivityServiceInterface {
	return &AdminActivityService{
		tierService:        tierService,
		taskService:        taskService,
		integrationService: integrationService,
		tierRepo:           tierRepo,
		taskRepo:           taskRepo,
		rewardRepo:         rewardRepo,
	}
}

// Data structures for admin operations
type AdminDashboardStats struct {
	TotalUsers               int                     `json:"total_users"`
	TotalActiveTasks         int                     `json:"total_active_tasks"`
	TotalPointsAwarded       int                     `json:"total_points_awarded"`
	TotalCashbackDistributed decimal.Decimal         `json:"total_cashback_distributed"`
	TierDistribution         []TierDistributionStats `json:"tier_distribution"`
	TaskCompletionStats      []TaskCompletionStats   `json:"task_completion_stats"`
	RecentActivities         []RecentActivityRecord  `json:"recent_activities"`
	SystemHealth             SystemHealthStats       `json:"system_health"`
}

type SystemHealthStats struct {
	Status          string     `json:"status"`
	LastProcessedAt *time.Time `json:"last_processed_at"`
	PendingTasks    int        `json:"pending_tasks"`
	ErrorCount      int        `json:"error_count"`
	Uptime          float64    `json:"uptime"`
}

type TierDistributionStats struct {
	Tier       model.ActivityTier `json:"tier"`
	UserCount  int                `json:"user_count"`
	Percentage float64            `json:"percentage"`
}

type TaskCompletionStats struct {
	Task                  model.ActivityTask `json:"task"`
	CompletionCount       int                `json:"completion_count"`
	CompletionRate        float64            `json:"completion_rate"`
	AverageTimeToComplete *float64           `json:"average_time_to_complete"`
}

type TierWithStats struct {
	Tier      model.ActivityTier `json:"tier"`
	UserCount int                `json:"user_count"`
}

type TaskAnalytics struct {
	Task                  model.ActivityTask    `json:"task"`
	TotalCompletions      int                   `json:"total_completions"`
	UniqueUsers           int                   `json:"unique_users"`
	AverageCompletionTime *float64              `json:"average_completion_time"`
	CompletionsByDay      []DailyCompletionStat `json:"completions_by_day"`
	TopUsers              []TaskTopUser         `json:"top_users"`
}

type DailyCompletionStat struct {
	Date        string `json:"date"`
	Completions int    `json:"completions"`
	UniqueUsers int    `json:"unique_users"`
}

type TaskTopUser struct {
	User              model.User `json:"user"`
	CompletionCount   int        `json:"completion_count"`
	TotalPointsEarned int        `json:"total_points_earned"`
	LastCompletedAt   time.Time  `json:"last_completed_at"`
}

type UserActivityDetails struct {
	User            model.User                   `json:"user"`
	ActivityLevel   model.UserActivityLevel      `json:"activity_level"`
	RecentTasks     []model.UserTaskProgress     `json:"recent_tasks"`
	PointHistory    []model.UserPointTransaction `json:"point_history"`
	TierHistory     []model.ActivityTierUpgrade  `json:"tier_history"`
	DailyActivities []model.UserDailyActivity    `json:"daily_activities"`
	Stats           model.UserActivityStats      `json:"stats"`
}

type RecentActivityRecord struct {
	ID            uuid.UUID `json:"id"`
	UserID        uuid.UUID `json:"user_id"`
	UserName      string    `json:"user_name"`
	ActivityType  string    `json:"activity_type"`
	Description   string    `json:"description"`
	PointsAwarded int       `json:"points_awarded"`
	Timestamp     time.Time `json:"timestamp"`
}

type AdminUserFilters struct {
	UserIDs   []uuid.UUID `json:"user_ids"`
	TierIDs   []uint      `json:"tier_ids"`
	MinPoints *int        `json:"min_points"`
	MaxPoints *int        `json:"max_points"`
	DateFrom  *time.Time  `json:"date_from"`
	DateTo    *time.Time  `json:"date_to"`
	Limit     int         `json:"limit"`
	Offset    int         `json:"offset"`
}

type AdminTaskFilters struct {
	Types      []model.TaskType     `json:"types"`
	Categories []model.TaskCategory `json:"categories"`
	Statuses   []model.TaskStatus   `json:"statuses"`
	DateFrom   *time.Time           `json:"date_from"`
	DateTo     *time.Time           `json:"date_to"`
	Limit      int                  `json:"limit"`
	Offset     int                  `json:"offset"`
}

// GetDashboardStats retrieves comprehensive dashboard statistics
func (s *AdminActivityService) GetDashboardStats(ctx context.Context) (*AdminDashboardStats, error) {
	global.GVA_LOG.Info("Getting admin dashboard stats")

	// Get total users with activity levels
	var totalUsers int64
	if err := global.GVA_DB.Model(&model.UserActivityLevel{}).Count(&totalUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count total users: %w", err)
	}

	// Get total active tasks
	var totalActiveTasks int64
	if err := global.GVA_DB.Model(&model.ActivityTask{}).Where("status = ?", model.TaskStatusActive).Count(&totalActiveTasks).Error; err != nil {
		return nil, fmt.Errorf("failed to count active tasks: %w", err)
	}

	// Get total points awarded
	var totalPointsResult struct {
		Total int
	}
	if err := global.GVA_DB.Model(&model.UserPointTransaction{}).
		Select("COALESCE(SUM(points), 0) as total").
		Where("points > 0").
		Scan(&totalPointsResult).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate total points: %w", err)
	}

	// Get total cashback distributed
	var totalCashbackResult struct {
		Total decimal.Decimal
	}
	if err := global.GVA_DB.Model(&model.ActivityReward{}).
		Select("COALESCE(SUM(amount_usd), 0) as total").
		Where("type = ? AND status = ?", model.ActivityRewardTypeCashback, model.ActivityRewardStatusClaimed).
		Scan(&totalCashbackResult).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate total cashback: %w", err)
	}

	// Get tier distribution
	tierDistribution, err := s.GetTierDistribution(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get tier distribution: %w", err)
	}

	// Get task completion stats
	taskStats, err := s.GetTaskCompletionStats(ctx, time.Now().AddDate(0, 0, -30), time.Now())
	if err != nil {
		return nil, fmt.Errorf("failed to get task completion stats: %w", err)
	}

	// Get recent activities
	recentActivities, err := s.GetRecentActivities(ctx, 10)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent activities: %w", err)
	}

	// Get system health
	systemHealth, err := s.GetSystemHealth(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get system health: %w", err)
	}

	return &AdminDashboardStats{
		TotalUsers:               int(totalUsers),
		TotalActiveTasks:         int(totalActiveTasks),
		TotalPointsAwarded:       totalPointsResult.Total,
		TotalCashbackDistributed: totalCashbackResult.Total,
		TierDistribution:         tierDistribution,
		TaskCompletionStats:      taskStats,
		RecentActivities:         recentActivities,
		SystemHealth:             *systemHealth,
	}, nil
}

// GetSystemHealth retrieves system health statistics
func (s *AdminActivityService) GetSystemHealth(ctx context.Context) (*SystemHealthStats, error) {
	// Get pending tasks count
	var pendingTasks int64
	if err := global.GVA_DB.Model(&model.UserTaskProgress{}).
		Where("status IN (?)", []model.UserTaskStatus{
			model.UserTaskStatusInProgress,
			model.UserTaskStatusCompleted,
		}).Count(&pendingTasks).Error; err != nil {
		return nil, fmt.Errorf("failed to count pending tasks: %w", err)
	}

	// Get error count (simplified - you might want to implement proper error tracking)
	errorCount := 0

	// Calculate uptime (simplified - you might want to implement proper uptime tracking)
	uptime := 99.9

	return &SystemHealthStats{
		Status:          "healthy",
		LastProcessedAt: timePtr(time.Now()),
		PendingTasks:    int(pendingTasks),
		ErrorCount:      errorCount,
		Uptime:          uptime,
	}, nil
}

// GetTierDistribution retrieves tier distribution statistics
func (s *AdminActivityService) GetTierDistribution(ctx context.Context) ([]TierDistributionStats, error) {
	tiers, err := s.tierService.GetAllTiers(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get tiers: %w", err)
	}

	var totalUsers int64
	if err := global.GVA_DB.Model(&model.UserActivityLevel{}).Count(&totalUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count total users: %w", err)
	}

	var distribution []TierDistributionStats
	for _, tier := range tiers {
		var userCount int64
		if err := global.GVA_DB.Model(&model.UserActivityLevel{}).
			Where("activity_tier_id = ?", tier.ID).
			Count(&userCount).Error; err != nil {
			continue
		}

		percentage := 0.0
		if totalUsers > 0 {
			percentage = float64(userCount) / float64(totalUsers) * 100
		}

		distribution = append(distribution, TierDistributionStats{
			Tier:       tier,
			UserCount:  int(userCount),
			Percentage: percentage,
		})
	}

	return distribution, nil
}

// GetTaskCompletionStats retrieves task completion statistics
func (s *AdminActivityService) GetTaskCompletionStats(ctx context.Context, dateFrom, dateTo time.Time) ([]TaskCompletionStats, error) {
	tasks, err := s.taskService.GetActiveTasks(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get active tasks: %w", err)
	}

	var stats []TaskCompletionStats
	for _, task := range tasks {
		var completionCount int64
		if err := global.GVA_DB.Model(&model.TaskCompletion{}).
			Where("task_id = ? AND completed_at BETWEEN ? AND ?", task.ID, dateFrom, dateTo).
			Count(&completionCount).Error; err != nil {
			continue
		}

		// Calculate completion rate (simplified)
		var totalAttempts int64
		if err := global.GVA_DB.Model(&model.UserTaskProgress{}).
			Where("task_id = ? AND created_at BETWEEN ? AND ?", task.ID, dateFrom, dateTo).
			Count(&totalAttempts).Error; err != nil {
			continue
		}

		completionRate := 0.0
		if totalAttempts > 0 {
			completionRate = float64(completionCount) / float64(totalAttempts) * 100
		}

		stats = append(stats, TaskCompletionStats{
			Task:            task,
			CompletionCount: int(completionCount),
			CompletionRate:  completionRate,
		})
	}

	return stats, nil
}

// CreateActivityTier creates a new activity tier
func (s *AdminActivityService) CreateActivityTier(ctx context.Context, tier *model.ActivityTier) error {
	global.GVA_LOG.Info("Admin creating activity tier", zap.String("name", tier.Name), zap.Int("level", tier.Level))

	if err := s.tierRepo.CreateTier(ctx, tier); err != nil {
		return fmt.Errorf("failed to create tier: %w", err)
	}

	global.GVA_LOG.Info("Activity tier created successfully", zap.Uint("tier_id", tier.ID))
	return nil
}

// UpdateActivityTier updates an existing activity tier
func (s *AdminActivityService) UpdateActivityTier(ctx context.Context, tier *model.ActivityTier) error {
	global.GVA_LOG.Info("Admin updating activity tier", zap.Uint("tier_id", tier.ID))

	if err := s.tierRepo.UpdateTier(ctx, tier); err != nil {
		return fmt.Errorf("failed to update tier: %w", err)
	}

	global.GVA_LOG.Info("Activity tier updated successfully", zap.Uint("tier_id", tier.ID))
	return nil
}

// DeleteActivityTier deletes an activity tier
func (s *AdminActivityService) DeleteActivityTier(ctx context.Context, tierID uint) error {
	global.GVA_LOG.Info("Admin deleting activity tier", zap.Uint("tier_id", tierID))

	// Check if any users are using this tier
	var userCount int64
	if err := global.GVA_DB.Model(&model.UserActivityLevel{}).
		Where("activity_tier_id = ?", tierID).
		Count(&userCount).Error; err != nil {
		return fmt.Errorf("failed to check tier usage: %w", err)
	}

	if userCount > 0 {
		return fmt.Errorf("cannot delete tier: %d users are currently using this tier", userCount)
	}

	if err := s.tierRepo.DeleteTier(ctx, tierID); err != nil {
		return fmt.Errorf("failed to delete tier: %w", err)
	}

	global.GVA_LOG.Info("Activity tier deleted successfully", zap.Uint("tier_id", tierID))
	return nil
}

// GetRecentActivities retrieves recent activity records
func (s *AdminActivityService) GetRecentActivities(ctx context.Context, limit int) ([]RecentActivityRecord, error) {
	var activities []RecentActivityRecord

	// Get recent point transactions
	var transactions []model.UserPointTransaction
	if err := global.GVA_DB.Preload("User").
		Order("created_at DESC").
		Limit(limit).
		Find(&transactions).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent transactions: %w", err)
	}

	for _, tx := range transactions {
		userName := "Unknown User"
		if tx.User.Email != nil && *tx.User.Email != "" {
			userName = *tx.User.Email
		}

		activities = append(activities, RecentActivityRecord{
			ID:            tx.ID,
			UserID:        tx.UserID,
			UserName:      userName,
			ActivityType:  tx.Type,
			Description:   tx.Description,
			PointsAwarded: tx.Points,
			Timestamp:     tx.CreatedAt,
		})
	}

	return activities, nil
}

// AdjustUserPoints manually adjusts user points
func (s *AdminActivityService) AdjustUserPoints(ctx context.Context, userID uuid.UUID, points int, reason, description string) error {
	global.GVA_LOG.Info("Admin adjusting user points",
		zap.String("user_id", userID.String()),
		zap.Int("points", points),
		zap.String("reason", reason))

	if err := s.tierService.UpdateUserPoints(ctx, userID, points, "ADMIN_ADJUSTMENT",
		fmt.Sprintf("Admin adjustment: %s - %s", reason, description), nil, "MANUAL"); err != nil {
		return fmt.Errorf("failed to adjust user points: %w", err)
	}

	global.GVA_LOG.Info("User points adjusted successfully", zap.String("user_id", userID.String()))
	return nil
}

// ResetUserTaskProgress resets user's progress on a specific task
func (s *AdminActivityService) ResetUserTaskProgress(ctx context.Context, userID, taskID uuid.UUID) error {
	global.GVA_LOG.Info("Admin resetting user task progress",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()))

	if err := s.taskRepo.DeleteUserTaskProgress(ctx, userID, taskID); err != nil {
		return fmt.Errorf("failed to reset user task progress: %w", err)
	}

	// Reinitialize the task for the user
	if err := s.integrationService.InitializeUserActivitySystem(ctx, userID); err != nil {
		global.GVA_LOG.Warn("Failed to reinitialize user activity system",
			zap.String("user_id", userID.String()),
			zap.Error(err))
	}

	global.GVA_LOG.Info("User task progress reset successfully",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()))
	return nil
}

// ForceUserTierUpgrade forces a user to upgrade to a specific tier
func (s *AdminActivityService) ForceUserTierUpgrade(ctx context.Context, userID uuid.UUID, tierLevel int) error {
	global.GVA_LOG.Info("Admin forcing user tier upgrade",
		zap.String("user_id", userID.String()),
		zap.Int("tier_level", tierLevel))

	// Get the target tier
	targetTier, err := s.tierRepo.GetTierByLevel(ctx, tierLevel)
	if err != nil {
		return fmt.Errorf("failed to get target tier: %w", err)
	}

	// Force upgrade
	if err := s.tierService.UpgradeUserTier(ctx, userID, targetTier.ID, "ADMIN_FORCE",
		fmt.Sprintf(`{"admin_action": true, "target_level": %d}`, tierLevel)); err != nil {
		return fmt.Errorf("failed to force tier upgrade: %w", err)
	}

	global.GVA_LOG.Info("User tier upgrade forced successfully",
		zap.String("user_id", userID.String()),
		zap.Int("tier_level", tierLevel))
	return nil
}

// CreateActivityTask creates a new activity task
func (s *AdminActivityService) CreateActivityTask(ctx context.Context, task *model.ActivityTask) error {
	global.GVA_LOG.Info("Admin creating activity task", zap.String("name", task.Name))

	if err := s.taskRepo.CreateTask(ctx, task); err != nil {
		return fmt.Errorf("failed to create task: %w", err)
	}

	global.GVA_LOG.Info("Activity task created successfully", zap.String("task_id", task.ID.String()))
	return nil
}

// UpdateActivityTask updates an existing activity task
func (s *AdminActivityService) UpdateActivityTask(ctx context.Context, task *model.ActivityTask) error {
	global.GVA_LOG.Info("Admin updating activity task", zap.String("task_id", task.ID.String()))

	if err := s.taskRepo.UpdateTask(ctx, task); err != nil {
		return fmt.Errorf("failed to update task: %w", err)
	}

	global.GVA_LOG.Info("Activity task updated successfully", zap.String("task_id", task.ID.String()))
	return nil
}

// DeleteActivityTask deletes an activity task
func (s *AdminActivityService) DeleteActivityTask(ctx context.Context, taskID uuid.UUID) error {
	global.GVA_LOG.Info("Admin deleting activity task", zap.String("task_id", taskID.String()))

	if err := s.taskRepo.DeleteTask(ctx, taskID); err != nil {
		return fmt.Errorf("failed to delete task: %w", err)
	}

	global.GVA_LOG.Info("Activity task deleted successfully", zap.String("task_id", taskID.String()))
	return nil
}

// ResetAllDailyTasks resets daily tasks for all users
func (s *AdminActivityService) ResetAllDailyTasks(ctx context.Context) error {
	global.GVA_LOG.Info("Admin resetting all daily tasks")

	if err := s.taskService.ResetDailyTasks(ctx); err != nil {
		return fmt.Errorf("failed to reset daily tasks: %w", err)
	}

	global.GVA_LOG.Info("All daily tasks reset successfully")
	return nil
}

// ProcessAllExpiredTasks processes all expired tasks
func (s *AdminActivityService) ProcessAllExpiredTasks(ctx context.Context) error {
	global.GVA_LOG.Info("Admin processing all expired tasks")

	if err := s.taskService.ProcessExpiredTasks(ctx); err != nil {
		return fmt.Errorf("failed to process expired tasks: %w", err)
	}

	global.GVA_LOG.Info("All expired tasks processed successfully")
	return nil
}

// RecalculateAllUserTiers recalculates tiers for all users
func (s *AdminActivityService) RecalculateAllUserTiers(ctx context.Context) error {
	global.GVA_LOG.Info("Admin recalculating all user tiers")

	var userIDs []uuid.UUID
	if err := global.GVA_DB.Model(&model.UserActivityLevel{}).Pluck("user_id", &userIDs).Error; err != nil {
		return fmt.Errorf("failed to get user IDs: %w", err)
	}

	for _, userID := range userIDs {
		newTier, err := s.tierService.CalculateUserTier(ctx, userID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate tier for user", zap.String("user_id", userID.String()), zap.Error(err))
			continue
		}

		// Get current user level
		userLevel, err := s.tierService.GetUserActivityLevel(ctx, userID)
		if err != nil {
			continue
		}

		// Upgrade if needed
		if newTier.ID != userLevel.ActivityTierID {
			if err := s.tierService.UpgradeUserTier(ctx, userID, newTier.ID, "ADMIN_RECALCULATION", "Admin tier recalculation"); err != nil {
				global.GVA_LOG.Warn("Failed to upgrade user tier", zap.String("user_id", userID.String()), zap.Error(err))
			}
		}
	}

	global.GVA_LOG.Info("All user tiers recalculated successfully")
	return nil
}

// InitializeUserActivity initializes activity system for a user
func (s *AdminActivityService) InitializeUserActivity(ctx context.Context, userID uuid.UUID) error {
	global.GVA_LOG.Info("Admin initializing user activity", zap.String("user_id", userID.String()))

	if err := s.integrationService.InitializeUserActivitySystem(ctx, userID); err != nil {
		return fmt.Errorf("failed to initialize user activity: %w", err)
	}

	global.GVA_LOG.Info("User activity initialized successfully", zap.String("user_id", userID.String()))
	return nil
}

// BulkInitializeUsers initializes activity system for multiple users
func (s *AdminActivityService) BulkInitializeUsers(ctx context.Context, userIDs []uuid.UUID) error {
	global.GVA_LOG.Info("Admin bulk initializing users", zap.Int("user_count", len(userIDs)))

	for _, userID := range userIDs {
		if err := s.integrationService.InitializeUserActivitySystem(ctx, userID); err != nil {
			global.GVA_LOG.Warn("Failed to initialize user activity", zap.String("user_id", userID.String()), zap.Error(err))
			continue
		}
	}

	global.GVA_LOG.Info("Bulk user initialization completed", zap.Int("user_count", len(userIDs)))
	return nil
}

// ExportUserActivities exports user activity data to CSV format
func (s *AdminActivityService) ExportUserActivities(ctx context.Context, filters *AdminUserFilters) (string, error) {
	users, _, err := s.GetUsersWithFilters(ctx, filters)
	if err != nil {
		return "", fmt.Errorf("failed to get users: %w", err)
	}

	var csvData strings.Builder
	csvData.WriteString("User ID,Email,Tier,Level,Total Points,Current Points,Total Trading Volume,Monthly Active Days,Total Cashback Earned,Created At\n")

	for _, user := range users {
		email := "N/A"
		if user.User.Email != nil {
			email = *user.User.Email
		}

		csvData.WriteString(fmt.Sprintf("%s,%s,%s,%d,%d,%d,%.2f,%d,%.2f,%s\n",
			user.User.ID.String(),
			email,
			user.ActivityLevel.ActivityTier.Name,
			user.ActivityLevel.ActivityTier.Level,
			user.ActivityLevel.TotalPoints,
			user.ActivityLevel.CurrentPoints,
			user.ActivityLevel.TotalTradingVolume.InexactFloat64(),
			user.ActivityLevel.MonthlyActiveDays,
			user.ActivityLevel.TotalCashbackEarned.InexactFloat64(),
			user.ActivityLevel.CreatedAt.Format("2006-01-02 15:04:05"),
		))
	}

	return csvData.String(), nil
}

// ExportTaskCompletions exports task completion data to CSV format
func (s *AdminActivityService) ExportTaskCompletions(ctx context.Context, filters *AdminTaskFilters) (string, error) {
	query := global.GVA_DB.Model(&model.TaskCompletion{}).
		Preload("User").
		Preload("Task")

	// Apply filters
	if len(filters.Types) > 0 {
		query = query.Joins("JOIN activity_tasks ON task_completions.task_id = activity_tasks.id").
			Where("activity_tasks.type IN (?)", filters.Types)
	}
	if filters.DateFrom != nil {
		query = query.Where("completed_at >= ?", *filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("completed_at <= ?", *filters.DateTo)
	}

	var completions []model.TaskCompletion
	if err := query.Offset(filters.Offset).Limit(filters.Limit).Find(&completions).Error; err != nil {
		return "", fmt.Errorf("failed to get task completions: %w", err)
	}

	var csvData strings.Builder
	csvData.WriteString("Completion ID,User ID,User Email,Task Name,Task Type,Points Awarded,Cashback Amount,Completed At\n")

	for _, completion := range completions {
		email := "N/A"
		if completion.User.Email != nil {
			email = *completion.User.Email
		}

		csvData.WriteString(fmt.Sprintf("%s,%s,%s,%s,%s,%d,%.2f,%s\n",
			completion.ID.String(),
			completion.UserID.String(),
			email,
			completion.Task.Name,
			completion.Task.Type,
			completion.PointsAwarded,
			completion.CashbackAmount.InexactFloat64(),
			completion.CompletedAt.Format("2006-01-02 15:04:05"),
		))
	}

	return csvData.String(), nil
}

// Helper function
func timePtr(t time.Time) *time.Time {
	return &t
}
