package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity"
)

// ActivityTaskServiceInterface defines the interface for activity task service
type ActivityTaskServiceInterface interface {
	// Task Management
	GetActiveTasks(ctx context.Context) ([]model.ActivityTask, error)
	GetTasksByCategory(ctx context.Context, category model.TaskCategory) ([]model.ActivityTask, error)
	CreateTask(ctx context.Context, task *model.ActivityTask) error
	UpdateTask(ctx context.Context, task *model.ActivityTask) error

	// User Task Progress
	GetUserTaskProgress(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error)
	GetUserTasksByCategory(ctx context.Context, userID uuid.UUID, category model.TaskCategory) ([]model.UserTaskProgress, error)
	InitializeUserTasks(ctx context.Context, userID uuid.UUID) error
	RefreshUserTasks(ctx context.Context, userID uuid.UUID) error

	// Task Completion and Progress
	UpdateTaskProgress(ctx context.Context, userID, taskID uuid.UUID, count int, volume decimal.Decimal, metadata string) error
	CompleteTask(ctx context.Context, userID, taskID uuid.UUID, triggerType, triggerSource string, referenceID *uuid.UUID) error
	ClaimTaskReward(ctx context.Context, userID, taskID uuid.UUID) error

	// Daily Task Management
	ResetDailyTasks(ctx context.Context) error
	ProcessExpiredTasks(ctx context.Context) error

	// Trading Activity Integration
	ProcessTradingActivity(ctx context.Context, userID uuid.UUID, volume decimal.Decimal, transactionCount int) error
	ProcessSocialActivity(ctx context.Context, userID uuid.UUID, activityType, activityData string) error
}

// ActivityTaskService implements the activity task service interface
type ActivityTaskService struct {
	taskRepo    activity.ActivityTaskRepositoryInterface
	tierService ActivityTierServiceInterface
}

// NewActivityTaskService creates a new activity task service
func NewActivityTaskService(taskRepo activity.ActivityTaskRepositoryInterface, tierService ActivityTierServiceInterface) ActivityTaskServiceInterface {
	return &ActivityTaskService{
		taskRepo:    taskRepo,
		tierService: tierService,
	}
}

// GetActiveTasks retrieves all active tasks
func (s *ActivityTaskService) GetActiveTasks(ctx context.Context) ([]model.ActivityTask, error) {
	return s.taskRepo.GetActiveTasks(ctx)
}

// GetTasksByCategory retrieves tasks by category
func (s *ActivityTaskService) GetTasksByCategory(ctx context.Context, category model.TaskCategory) ([]model.ActivityTask, error) {
	return s.taskRepo.GetTasksByCategory(ctx, category)
}

// CreateTask creates a new task
func (s *ActivityTaskService) CreateTask(ctx context.Context, task *model.ActivityTask) error {
	return s.taskRepo.CreateTask(ctx, task)
}

// UpdateTask updates an existing task
func (s *ActivityTaskService) UpdateTask(ctx context.Context, task *model.ActivityTask) error {
	return s.taskRepo.UpdateTask(ctx, task)
}

// GetUserTaskProgress retrieves user's task progress
func (s *ActivityTaskService) GetUserTaskProgress(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	return s.taskRepo.GetUserTaskProgressByUser(ctx, userID)
}

// GetUserTasksByCategory retrieves user's tasks by category
func (s *ActivityTaskService) GetUserTasksByCategory(ctx context.Context, userID uuid.UUID, category model.TaskCategory) ([]model.UserTaskProgress, error) {
	// Get all user progress
	allProgress, err := s.taskRepo.GetUserTaskProgressByUser(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user task progress: %w", err)
	}

	// Filter by category
	var filteredProgress []model.UserTaskProgress
	for _, progress := range allProgress {
		if progress.Task.Category == category {
			filteredProgress = append(filteredProgress, progress)
		}
	}

	return filteredProgress, nil
}

// InitializeUserTasks initializes tasks for a new user
func (s *ActivityTaskService) InitializeUserTasks(ctx context.Context, userID uuid.UUID) error {
	// Get all active tasks
	tasks, err := s.taskRepo.GetActiveTasks(ctx)
	if err != nil {
		return fmt.Errorf("failed to get active tasks: %w", err)
	}

	now := time.Now()
	for _, task := range tasks {
		// Check if user already has progress for this task
		_, err := s.taskRepo.GetUserTaskProgress(ctx, userID, task.ID)
		if err == nil {
			continue // Progress already exists
		}

		// Create initial progress
		progress := &model.UserTaskProgress{
			UserID:        userID,
			TaskID:        task.ID,
			Status:        model.UserTaskStatusNotStarted,
			CurrentCount:  0,
			CurrentVolume: decimal.Zero,
		}

		// Set period for daily tasks
		if task.Type == model.TaskTypeDaily {
			startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
			endOfDay := startOfDay.Add(24 * time.Hour)
			progress.PeriodStart = &startOfDay
			progress.PeriodEnd = &endOfDay
		}

		// Set expiration for time-limited tasks
		if task.TimeWindowHours != nil {
			expiresAt := now.Add(time.Duration(*task.TimeWindowHours) * time.Hour)
			progress.ExpiresAt = &expiresAt
		}

		if err := s.taskRepo.CreateUserTaskProgress(ctx, progress); err != nil {
			global.GVA_LOG.Error("Failed to create user task progress",
				zap.String("user_id", userID.String()),
				zap.String("task_id", task.ID.String()),
				zap.Error(err))
			continue
		}
	}

	global.GVA_LOG.Info("Initialized user tasks", zap.String("user_id", userID.String()), zap.Int("task_count", len(tasks)))
	return nil
}

// RefreshUserTasks refreshes user's task list and resets daily tasks
func (s *ActivityTaskService) RefreshUserTasks(ctx context.Context, userID uuid.UUID) error {
	// Initialize any new tasks
	if err := s.InitializeUserTasks(ctx, userID); err != nil {
		return fmt.Errorf("failed to initialize user tasks: %w", err)
	}

	// Reset daily tasks
	now := time.Now()
	if err := s.taskRepo.ResetDailyTasks(ctx, userID, now); err != nil {
		return fmt.Errorf("failed to reset daily tasks: %w", err)
	}

	global.GVA_LOG.Info("Refreshed user tasks", zap.String("user_id", userID.String()))
	return nil
}

// UpdateTaskProgress updates progress on a specific task
func (s *ActivityTaskService) UpdateTaskProgress(ctx context.Context, userID, taskID uuid.UUID, count int, volume decimal.Decimal, metadata string) error {
	// Get current progress
	progress, err := s.taskRepo.GetUserTaskProgress(ctx, userID, taskID)
	if err != nil {
		return fmt.Errorf("failed to get user task progress: %w", err)
	}

	// Check if task is still active and not expired
	if progress.Status == model.UserTaskStatusCompleted || progress.Status == model.UserTaskStatusClaimed || progress.Status == model.UserTaskStatusExpired {
		return fmt.Errorf("task is not in progress")
	}

	// Update progress
	progress.CurrentCount += count
	progress.CurrentVolume = progress.CurrentVolume.Add(volume)
	progress.Status = model.UserTaskStatusInProgress
	now := time.Now()
	progress.LastUpdateAt = &now

	if progress.StartedAt == nil {
		progress.StartedAt = &now
	}

	// Update metadata if provided
	if metadata != "" {
		progress.Metadata = metadata
	}

	// Check if task is completed
	task := progress.Task
	isCompleted := false

	switch task.Type {
	case model.TaskTypeDaily, model.TaskTypeOneTime, model.TaskTypeProgress:
		if progress.CurrentCount >= task.RequiredCount {
			if task.RequiredVolume.IsZero() || progress.CurrentVolume.GreaterThanOrEqual(task.RequiredVolume) {
				isCompleted = true
			}
		}
	case model.TaskTypeUnlimited:
		// Unlimited tasks complete each time requirements are met
		if progress.CurrentCount >= task.RequiredCount {
			if task.RequiredVolume.IsZero() || progress.CurrentVolume.GreaterThanOrEqual(task.RequiredVolume) {
				isCompleted = true
			}
		}
	}

	if isCompleted {
		progress.Status = model.UserTaskStatusCompleted
		progress.CompletedAt = &now
		progress.CompletedCount++

		// For unlimited tasks, reset progress for next completion
		if task.Type == model.TaskTypeUnlimited {
			progress.CurrentCount = 0
			progress.CurrentVolume = decimal.Zero
			progress.Status = model.UserTaskStatusNotStarted
		}
	}

	// Save progress
	if err := s.taskRepo.UpdateUserTaskProgress(ctx, progress); err != nil {
		return fmt.Errorf("failed to update user task progress: %w", err)
	}

	global.GVA_LOG.Info("Updated task progress",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("count", count),
		zap.String("volume", volume.String()),
		zap.Bool("completed", isCompleted))

	return nil
}

// CompleteTask manually completes a task (for manual verification tasks)
func (s *ActivityTaskService) CompleteTask(ctx context.Context, userID, taskID uuid.UUID, triggerType, triggerSource string, referenceID *uuid.UUID) error {
	// Get current progress
	progress, err := s.taskRepo.GetUserTaskProgress(ctx, userID, taskID)
	if err != nil {
		return fmt.Errorf("failed to get user task progress: %w", err)
	}

	if progress.Status == model.UserTaskStatusCompleted || progress.Status == model.UserTaskStatusClaimed {
		return fmt.Errorf("task already completed")
	}

	// Mark as completed
	now := time.Now()
	progress.Status = model.UserTaskStatusCompleted
	progress.CompletedAt = &now
	progress.CompletedCount++

	// Create completion record
	completion := &model.TaskCompletion{
		UserID:        userID,
		TaskID:        taskID,
		CompletedAt:   now,
		PointsAwarded: progress.Task.PointReward,
		TriggerType:   triggerType,
		TriggerSource: triggerSource,
		ReferenceID:   referenceID,
	}

	if err := s.taskRepo.CreateTaskCompletion(ctx, completion); err != nil {
		return fmt.Errorf("failed to create task completion: %w", err)
	}

	// Save progress
	if err := s.taskRepo.UpdateUserTaskProgress(ctx, progress); err != nil {
		return fmt.Errorf("failed to update user task progress: %w", err)
	}

	global.GVA_LOG.Info("Task completed manually",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.String("trigger", triggerType))

	return nil
}

// ClaimTaskReward claims the reward for a completed task
func (s *ActivityTaskService) ClaimTaskReward(ctx context.Context, userID, taskID uuid.UUID) error {
	// Get current progress
	progress, err := s.taskRepo.GetUserTaskProgress(ctx, userID, taskID)
	if err != nil {
		return fmt.Errorf("failed to get user task progress: %w", err)
	}

	if progress.Status != model.UserTaskStatusCompleted {
		return fmt.Errorf("task is not completed")
	}

	// Award points
	if progress.Task.PointReward > 0 {
		if err := s.tierService.UpdateUserPoints(ctx, userID, progress.Task.PointReward,
			"TASK_COMPLETION",
			fmt.Sprintf("Completed task: %s", progress.Task.Name),
			&taskID,
			"TASK"); err != nil {
			return fmt.Errorf("failed to award points: %w", err)
		}
		progress.PointsEarned += progress.Task.PointReward
	}

	// Award cashback if applicable
	if !progress.Task.CashbackReward.IsZero() {
		progress.CashbackEarned = progress.CashbackEarned.Add(progress.Task.CashbackReward)
		progress.TotalRewardUSD = progress.TotalRewardUSD.Add(progress.Task.CashbackReward)
	}

	// Mark as claimed
	now := time.Now()
	progress.Status = model.UserTaskStatusClaimed
	progress.ClaimedAt = &now

	// Save progress
	if err := s.taskRepo.UpdateUserTaskProgress(ctx, progress); err != nil {
		return fmt.Errorf("failed to update user task progress: %w", err)
	}

	global.GVA_LOG.Info("Task reward claimed",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("points", progress.Task.PointReward),
		zap.String("cashback", progress.Task.CashbackReward.String()))

	return nil
}

// ResetDailyTasks resets all daily tasks for all users
func (s *ActivityTaskService) ResetDailyTasks(ctx context.Context) error {
	// This would typically be called by a cron job at UTC 0:00
	global.GVA_LOG.Info("Starting daily task reset")

	// Get all daily tasks
	dailyTasks, err := s.taskRepo.GetTasksByType(ctx, model.TaskTypeDaily)
	if err != nil {
		return fmt.Errorf("failed to get daily tasks: %w", err)
	}

	// This is a simplified implementation
	// In production, you'd want to batch process users
	global.GVA_LOG.Info("Daily task reset completed", zap.Int("task_count", len(dailyTasks)))
	return nil
}

// ProcessExpiredTasks processes and marks expired tasks
func (s *ActivityTaskService) ProcessExpiredTasks(ctx context.Context) error {
	currentTime := time.Now()
	expiredTasks, err := s.taskRepo.GetExpiredTasks(ctx, currentTime)
	if err != nil {
		return fmt.Errorf("failed to get expired tasks: %w", err)
	}

	for _, progress := range expiredTasks {
		progress.Status = model.UserTaskStatusExpired
		if err := s.taskRepo.UpdateUserTaskProgress(ctx, &progress); err != nil {
			global.GVA_LOG.Error("Failed to mark task as expired",
				zap.String("user_id", progress.UserID.String()),
				zap.String("task_id", progress.TaskID.String()),
				zap.Error(err))
			continue
		}
	}

	global.GVA_LOG.Info("Processed expired tasks", zap.Int("count", len(expiredTasks)))
	return nil
}

// ProcessTradingActivity processes trading activity and updates relevant tasks
func (s *ActivityTaskService) ProcessTradingActivity(ctx context.Context, userID uuid.UUID, volume decimal.Decimal, transactionCount int) error {
	// Get user's trading tasks
	tradingTasks, err := s.GetUserTasksByCategory(ctx, userID, model.TaskCategoryTrading)
	if err != nil {
		return fmt.Errorf("failed to get trading tasks: %w", err)
	}

	for _, progress := range tradingTasks {
		if progress.Status == model.UserTaskStatusCompleted || progress.Status == model.UserTaskStatusClaimed || progress.Status == model.UserTaskStatusExpired {
			continue
		}

		// Update progress based on task requirements
		metadata := fmt.Sprintf(`{"volume": "%s", "transactions": %d}`, volume.String(), transactionCount)
		if err := s.UpdateTaskProgress(ctx, userID, progress.TaskID, transactionCount, volume, metadata); err != nil {
			global.GVA_LOG.Error("Failed to update trading task progress",
				zap.String("user_id", userID.String()),
				zap.String("task_id", progress.TaskID.String()),
				zap.Error(err))
			continue
		}
	}

	return nil
}

// ProcessSocialActivity processes social media activity and updates relevant tasks
func (s *ActivityTaskService) ProcessSocialActivity(ctx context.Context, userID uuid.UUID, activityType, activityData string) error {
	// Get user's community tasks
	communityTasks, err := s.GetUserTasksByCategory(ctx, userID, model.TaskCategoryCommunity)
	if err != nil {
		return fmt.Errorf("failed to get community tasks: %w", err)
	}

	for _, progress := range communityTasks {
		if progress.Status == model.UserTaskStatusCompleted || progress.Status == model.UserTaskStatusClaimed || progress.Status == model.UserTaskStatusExpired {
			continue
		}

		// Check if this task is relevant to the activity type
		var taskConfig map[string]interface{}
		if progress.Task.Config != "" {
			if err := json.Unmarshal([]byte(progress.Task.Config), &taskConfig); err != nil {
				continue
			}
		}

		// Simple matching - in production you'd have more sophisticated logic
		if taskConfig["activity_type"] == activityType {
			metadata := fmt.Sprintf(`{"activity_type": "%s", "data": "%s"}`, activityType, activityData)
			if err := s.UpdateTaskProgress(ctx, userID, progress.TaskID, 1, decimal.Zero, metadata); err != nil {
				global.GVA_LOG.Error("Failed to update social task progress",
					zap.String("user_id", userID.String()),
					zap.String("task_id", progress.TaskID.String()),
					zap.Error(err))
				continue
			}
		}
	}

	return nil
}
