package activity

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity"
)

// ActivityIntegrationService handles integration between activity system and existing systems
type ActivityIntegrationService struct {
	tierService ActivityTierServiceInterface
	taskService ActivityTaskServiceInterface
	rewardRepo  activity.ActivityRewardRepositoryInterface
	taskRepo    activity.ActivityTaskRepositoryInterface
	tierRepo    activity.ActivityTierRepositoryInterface
}

// NewActivityIntegrationService creates a new activity integration service
func NewActivityIntegrationService(
	tierService ActivityTierServiceInterface,
	taskService ActivityTaskServiceInterface,
	rewardRepo activity.ActivityRewardRepositoryInterface,
	taskRepo activity.ActivityTaskRepositoryInterface,
	tierRepo activity.ActivityTierRepositoryInterface,
) *ActivityIntegrationService {
	return &ActivityIntegrationService{
		tierService: tierService,
		taskService: taskService,
		rewardRepo:  rewardRepo,
		taskRepo:    taskRepo,
		tierRepo:    tierRepo,
	}
}

// InitializeUserActivitySystem initializes activity system for a new user
func (s *ActivityIntegrationService) InitializeUserActivitySystem(ctx context.Context, userID uuid.UUID) error {
	global.GVA_LOG.Info("Initializing activity system for user", zap.String("user_id", userID.String()))

	// Initialize user activity level (tier system)
	if _, err := s.tierService.InitializeUserActivityLevel(ctx, userID); err != nil {
		return fmt.Errorf("failed to initialize user activity level: %w", err)
	}

	// Initialize user tasks
	if err := s.taskService.InitializeUserTasks(ctx, userID); err != nil {
		return fmt.Errorf("failed to initialize user tasks: %w", err)
	}

	// Initialize user activity stats
	if err := s.initializeUserActivityStats(ctx, userID); err != nil {
		return fmt.Errorf("failed to initialize user activity stats: %w", err)
	}

	global.GVA_LOG.Info("Activity system initialized for user", zap.String("user_id", userID.String()))
	return nil
}

// ProcessTradingTransaction processes a trading transaction for activity rewards
func (s *ActivityIntegrationService) ProcessTradingTransaction(ctx context.Context, transaction *model.AffiliateTransaction) error {
	if transaction == nil {
		return nil
	}

	global.GVA_LOG.Info("Processing trading transaction for activity system",
		zap.String("user_id", transaction.UserID.String()),
		zap.String("order_id", transaction.OrderID.String()),
		zap.String("quote_amount", transaction.QuoteAmount.String()))

	// Update trading tasks
	if err := s.taskService.ProcessTradingActivity(ctx, transaction.UserID, transaction.QuoteAmount, 1); err != nil {
		global.GVA_LOG.Error("Failed to process trading activity for tasks",
			zap.String("user_id", transaction.UserID.String()),
			zap.Error(err))
	}

	// Update daily activity tracking
	if err := s.updateUserDailyActivity(ctx, transaction.UserID, "trading", transaction.QuoteAmount, 1); err != nil {
		global.GVA_LOG.Error("Failed to update daily activity",
			zap.String("user_id", transaction.UserID.String()),
			zap.Error(err))
	}

	// Calculate and award activity cashback based on user's tier
	if err := s.processActivityCashback(ctx, transaction); err != nil {
		global.GVA_LOG.Error("Failed to process activity cashback",
			zap.String("user_id", transaction.UserID.String()),
			zap.Error(err))
	}

	return nil
}

// ProcessUserLogin processes user login for activity tracking
func (s *ActivityIntegrationService) ProcessUserLogin(ctx context.Context, userID uuid.UUID) error {
	global.GVA_LOG.Info("Processing user login for activity system", zap.String("user_id", userID.String()))

	// Update daily login tasks
	if err := s.processLoginTasks(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to process login tasks",
			zap.String("user_id", userID.String()),
			zap.Error(err))
	}

	// Update daily activity tracking
	if err := s.updateUserDailyActivity(ctx, userID, "login", decimal.Zero, 0); err != nil {
		global.GVA_LOG.Error("Failed to update daily activity for login",
			zap.String("user_id", userID.String()),
			zap.Error(err))
	}

	return nil
}

// ProcessSocialActivity processes social media activity
func (s *ActivityIntegrationService) ProcessSocialActivity(ctx context.Context, userID uuid.UUID, activityType, activityData string) error {
	global.GVA_LOG.Info("Processing social activity for activity system",
		zap.String("user_id", userID.String()),
		zap.String("activity_type", activityType))

	// Update social/community tasks
	if err := s.taskService.ProcessSocialActivity(ctx, userID, activityType, activityData); err != nil {
		global.GVA_LOG.Error("Failed to process social activity for tasks",
			zap.String("user_id", userID.String()),
			zap.Error(err))
	}

	// Update daily activity tracking
	if err := s.updateUserDailyActivity(ctx, userID, "social", decimal.Zero, 0); err != nil {
		global.GVA_LOG.Error("Failed to update daily activity for social",
			zap.String("user_id", userID.String()),
			zap.Error(err))
	}

	return nil
}

// GetUserCashbackRate gets the user's current cashback rate based on their activity tier
func (s *ActivityIntegrationService) GetUserCashbackRate(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	return s.tierService.GetUserCashbackPercentage(ctx, userID)
}

// CalculateActivityCashback calculates activity cashback for a transaction
func (s *ActivityIntegrationService) CalculateActivityCashback(ctx context.Context, userID uuid.UUID, tradingFeeAmount decimal.Decimal) (decimal.Decimal, error) {
	return s.tierService.CalculateCashbackAmount(ctx, userID, tradingFeeAmount)
}

// initializeUserActivityStats initializes activity statistics for a new user
func (s *ActivityIntegrationService) initializeUserActivityStats(ctx context.Context, userID uuid.UUID) error {
	stats := &model.UserActivityStats{
		UserID: userID,
		// All other fields will use their default values
	}

	return s.rewardRepo.CreateUserActivityStats(ctx, stats)
}

// processActivityCashback processes activity cashback for a transaction
func (s *ActivityIntegrationService) processActivityCashback(ctx context.Context, transaction *model.AffiliateTransaction) error {
	// This would integrate with the existing activity cashback system
	// For now, we'll create a placeholder implementation

	// Get user's cashback rate
	cashbackRate, err := s.GetUserCashbackRate(ctx, transaction.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user cashback rate: %w", err)
	}

	if cashbackRate.IsZero() {
		return nil // No cashback for this tier
	}

	// Calculate cashback amount (this would typically be based on trading fees)
	// For now, we'll use a simple calculation
	tradingFee := transaction.QuoteAmount.Mul(decimal.NewFromFloat(0.001)) // Assume 0.1% trading fee
	cashbackAmount := tradingFee.Mul(cashbackRate)

	if cashbackAmount.IsZero() {
		return nil
	}

	// Create activity reward
	// Since AffiliateTransaction.ID is uint, we'll use the OrderID (which is UUID) as source reference
	reward := &model.ActivityReward{
		UserID:      transaction.UserID,
		Type:        model.ActivityRewardTypeCashback,
		Status:      model.ActivityRewardStatusPending,
		Amount:      cashbackAmount,
		AmountUSD:   cashbackAmount,
		Currency:    "USD",
		SourceType:  "TRADING",
		SourceID:    &transaction.OrderID, // Use OrderID instead of ID since it's a UUID
		SourceName:  "Trading Activity Cashback",
		EarnedAt:    time.Now(),
		Description: fmt.Sprintf("Activity cashback for trading transaction %s", transaction.OrderID.String()),
		Metadata:    fmt.Sprintf(`{"affiliate_transaction_id": %d, "order_id": "%s"}`, transaction.ID, transaction.OrderID.String()),
	}

	return s.rewardRepo.CreateActivityReward(ctx, reward)
}

// processLoginTasks processes login-related tasks
func (s *ActivityIntegrationService) processLoginTasks(ctx context.Context, userID uuid.UUID) error {
	// Get user's daily tasks
	dailyTasks, err := s.taskService.GetUserTasksByCategory(ctx, userID, model.TaskCategoryDaily)
	if err != nil {
		return fmt.Errorf("failed to get daily tasks: %w", err)
	}

	// Process login-related tasks
	for _, progress := range dailyTasks {
		if progress.Status == model.UserTaskStatusCompleted || progress.Status == model.UserTaskStatusClaimed {
			continue
		}

		// Check if this is a login task (simplified check)
		if progress.Task.Name == "Daily Login" || progress.Task.Type == model.TaskTypeDaily {
			// Update task progress
			if err := s.taskService.UpdateTaskProgress(ctx, userID, progress.TaskID, 1, decimal.Zero, `{"activity": "login"}`); err != nil {
				global.GVA_LOG.Error("Failed to update login task progress",
					zap.String("user_id", userID.String()),
					zap.String("task_id", progress.TaskID.String()),
					zap.Error(err))
			}
		}
	}

	return nil
}

// updateUserDailyActivity updates user's daily activity record
func (s *ActivityIntegrationService) updateUserDailyActivity(ctx context.Context, userID uuid.UUID, activityType string, volume decimal.Decimal, transactionCount int) error {
	today := time.Now().UTC()
	dateOnly := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, time.UTC)

	// Get or create daily activity record
	activity, err := s.rewardRepo.GetUserDailyActivity(ctx, userID, dateOnly)
	if err != nil {
		// Create new record
		activity = &model.UserDailyActivity{
			UserID: userID,
			Date:   dateOnly,
		}
	}

	// Update activity flags and metrics
	now := time.Now()
	switch activityType {
	case "login":
		activity.HasLogin = true
	case "trading":
		activity.HasTrading = true
		activity.TradingVolume = activity.TradingVolume.Add(volume)
		activity.TransactionCount += transactionCount
	case "social":
		activity.HasSocialActivity = true
	case "task":
		activity.HasTaskCompletion = true
		activity.TasksCompleted++
	}

	// Update timestamps
	if activity.FirstActivityAt == nil {
		activity.FirstActivityAt = &now
	}
	activity.LastActivityAt = &now

	// Save or update the record
	if activity.ID == uuid.Nil {
		return s.rewardRepo.CreateUserDailyActivity(ctx, activity)
	} else {
		return s.rewardRepo.UpdateUserDailyActivity(ctx, activity)
	}
}

// CreateDefaultTasks creates default tasks for the system
func (s *ActivityIntegrationService) CreateDefaultTasks(ctx context.Context) error {
	global.GVA_LOG.Info("Creating default activity tasks")

	defaultTasks := []model.ActivityTask{
		{
			Name:          "Daily Login",
			Description:   "Log in to the platform daily",
			Type:          model.TaskTypeDaily,
			Category:      model.TaskCategoryDaily,
			Status:        model.TaskStatusActive,
			RequiredCount: 1,
			PointReward:   10,
			Priority:      100,
			IsHighlight:   true,
			Icon:          "login",
			Color:         "#4CAF50",
		},
		{
			Name:          "Complete 3 Trades",
			Description:   "Execute 3 trading transactions",
			Type:          model.TaskTypeDaily,
			Category:      model.TaskCategoryTrading,
			Status:        model.TaskStatusActive,
			RequiredCount: 3,
			PointReward:   50,
			Priority:      90,
			Icon:          "trading",
			Color:         "#2196F3",
		},
		{
			Name:           "Trade $100 Volume",
			Description:    "Achieve $100 in trading volume",
			Type:           model.TaskTypeDaily,
			Category:       model.TaskCategoryTrading,
			Status:         model.TaskStatusActive,
			RequiredCount:  1,
			RequiredVolume: decimal.NewFromFloat(100),
			PointReward:    75,
			Priority:       85,
			Icon:           "volume",
			Color:          "#FF9800",
		},
		{
			Name:          "Follow on Twitter",
			Description:   "Follow our official Twitter account",
			Type:          model.TaskTypeOneTime,
			Category:      model.TaskCategoryCommunity,
			Status:        model.TaskStatusActive,
			RequiredCount: 1,
			PointReward:   100,
			Priority:      70,
			Icon:          "twitter",
			Color:         "#1DA1F2",
		},
	}

	for _, task := range defaultTasks {
		if err := s.taskService.CreateTask(ctx, &task); err != nil {
			global.GVA_LOG.Error("Failed to create default task",
				zap.String("task_name", task.Name),
				zap.Error(err))
			continue
		}
		global.GVA_LOG.Info("Created default task", zap.String("task_name", task.Name))
	}

	return nil
}
