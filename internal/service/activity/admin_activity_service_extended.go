package activity

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// Extended methods for AdminActivityService

// BulkUpdateTasks performs bulk operations on tasks
func (s *AdminActivityService) BulkUpdateTasks(ctx context.Context, taskIDs []uuid.UUID, operation string, newStatus *model.TaskStatus) error {
	global.GVA_LOG.Info("Admin performing bulk task operation",
		zap.String("operation", operation),
		zap.Int("task_count", len(taskIDs)))

	switch operation {
	case "ACTIVATE":
		if newStatus == nil {
			status := model.TaskStatusActive
			newStatus = &status
		}
		fallthrough
	case "DEACTIVATE":
		if newStatus == nil {
			status := model.TaskStatusInactive
			newStatus = &status
		}
		fallthrough
	case "ARCHIVE":
		if newStatus == nil {
			status := model.TaskStatusArchived
			newStatus = &status
		}
		// Update task status
		if err := global.GVA_DB.Model(&model.ActivityTask{}).
			Where("id IN (?)", taskIDs).
			Update("status", *newStatus).Error; err != nil {
			return fmt.Errorf("failed to update task status: %w", err)
		}

	case "DELETE":
		// Delete tasks
		if err := global.GVA_DB.Where("id IN (?)", taskIDs).Delete(&model.ActivityTask{}).Error; err != nil {
			return fmt.Errorf("failed to delete tasks: %w", err)
		}

	default:
		return fmt.Errorf("unknown operation: %s", operation)
	}

	global.GVA_LOG.Info("Bulk task operation completed successfully",
		zap.String("operation", operation),
		zap.Int("task_count", len(taskIDs)))
	return nil
}

// GetTaskAnalytics retrieves detailed analytics for a specific task
func (s *AdminActivityService) GetTaskAnalytics(ctx context.Context, taskID uuid.UUID) (*TaskAnalytics, error) {
	// Get task
	task, err := s.taskRepo.GetTaskByID(ctx, taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	// Get total completions
	var totalCompletions int64
	if err := global.GVA_DB.Model(&model.TaskCompletion{}).
		Where("task_id = ?", taskID).
		Count(&totalCompletions).Error; err != nil {
		return nil, fmt.Errorf("failed to count completions: %w", err)
	}

	// Get unique users
	var uniqueUsers int64
	if err := global.GVA_DB.Model(&model.TaskCompletion{}).
		Where("task_id = ?", taskID).
		Distinct("user_id").
		Count(&uniqueUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to count unique users: %w", err)
	}

	// Get completions by day (last 30 days)
	var dailyStats []DailyCompletionStat
	for i := 29; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i)
		startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.UTC)
		endOfDay := startOfDay.Add(24 * time.Hour)

		var completions int64
		var users int64

		global.GVA_DB.Model(&model.TaskCompletion{}).
			Where("task_id = ? AND completed_at BETWEEN ? AND ?", taskID, startOfDay, endOfDay).
			Count(&completions)

		global.GVA_DB.Model(&model.TaskCompletion{}).
			Where("task_id = ? AND completed_at BETWEEN ? AND ?", taskID, startOfDay, endOfDay).
			Distinct("user_id").
			Count(&users)

		dailyStats = append(dailyStats, DailyCompletionStat{
			Date:        startOfDay.Format("2006-01-02"),
			Completions: int(completions),
			UniqueUsers: int(users),
		})
	}

	// Get top users
	var topUsers []TaskTopUser
	var completionData []struct {
		UserID          uuid.UUID `gorm:"column:user_id"`
		CompletionCount int       `gorm:"column:completion_count"`
		TotalPoints     int       `gorm:"column:total_points"`
		LastCompletedAt time.Time `gorm:"column:last_completed_at"`
	}

	if err := global.GVA_DB.Model(&model.TaskCompletion{}).
		Select("user_id, COUNT(*) as completion_count, SUM(points_awarded) as total_points, MAX(completed_at) as last_completed_at").
		Where("task_id = ?", taskID).
		Group("user_id").
		Order("completion_count DESC").
		Limit(10).
		Scan(&completionData).Error; err != nil {
		return nil, fmt.Errorf("failed to get top users: %w", err)
	}

	for _, data := range completionData {
		var user model.User
		if err := global.GVA_DB.First(&user, "id = ?", data.UserID).Error; err != nil {
			continue
		}

		topUsers = append(topUsers, TaskTopUser{
			User:              user,
			CompletionCount:   data.CompletionCount,
			TotalPointsEarned: data.TotalPoints,
			LastCompletedAt:   data.LastCompletedAt,
		})
	}

	return &TaskAnalytics{
		Task:             *task,
		TotalCompletions: int(totalCompletions),
		UniqueUsers:      int(uniqueUsers),
		CompletionsByDay: dailyStats,
		TopUsers:         topUsers,
	}, nil
}

// GetUserActivityDetails retrieves detailed activity information for a user
func (s *AdminActivityService) GetUserActivityDetails(ctx context.Context, userID uuid.UUID) (*UserActivityDetails, error) {
	// Get user
	var user model.User
	if err := global.GVA_DB.First(&user, "id = ?", userID).Error; err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Get activity level
	activityLevel, err := s.tierService.GetUserActivityLevel(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get activity level: %w", err)
	}

	// Get recent tasks
	recentTasks, err := s.taskRepo.GetUserTaskProgressByUser(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent tasks: %w", err)
	}

	// Get point history
	pointHistory, err := s.tierRepo.GetUserPointTransactions(ctx, userID, 50, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get point history: %w", err)
	}

	// Get tier history
	tierHistory, err := s.tierRepo.GetUserTierUpgrades(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tier history: %w", err)
	}

	// Get daily activities (last 30 days)
	var dailyActivities []model.UserDailyActivity
	startDate := time.Now().AddDate(0, 0, -30)
	if err := global.GVA_DB.Where("user_id = ? AND date >= ?", userID, startDate).
		Order("date DESC").
		Find(&dailyActivities).Error; err != nil {
		return nil, fmt.Errorf("failed to get daily activities: %w", err)
	}

	// Get stats
	stats, err := s.rewardRepo.GetUserActivityStats(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user stats: %w", err)
	}

	return &UserActivityDetails{
		User:            user,
		ActivityLevel:   *activityLevel,
		RecentTasks:     recentTasks,
		PointHistory:    pointHistory,
		TierHistory:     tierHistory,
		DailyActivities: dailyActivities,
		Stats:           *stats,
	}, nil
}

// GetUsersWithFilters retrieves users with filtering options
func (s *AdminActivityService) GetUsersWithFilters(ctx context.Context, filters *AdminUserFilters) ([]UserActivityDetails, int, error) {
	query := global.GVA_DB.Model(&model.UserActivityLevel{}).
		Preload("User").
		Preload("ActivityTier")

	// Apply filters
	if len(filters.UserIDs) > 0 {
		query = query.Where("user_id IN (?)", filters.UserIDs)
	}
	if len(filters.TierIDs) > 0 {
		query = query.Where("activity_tier_id IN (?)", filters.TierIDs)
	}
	if filters.MinPoints != nil {
		query = query.Where("current_points >= ?", *filters.MinPoints)
	}
	if filters.MaxPoints != nil {
		query = query.Where("current_points <= ?", *filters.MaxPoints)
	}
	if filters.DateFrom != nil {
		query = query.Where("created_at >= ?", *filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("created_at <= ?", *filters.DateTo)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// Get users with pagination
	var userLevels []model.UserActivityLevel
	if err := query.Offset(filters.Offset).Limit(filters.Limit).Find(&userLevels).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}

	// Convert to detailed format
	var results []UserActivityDetails
	for _, level := range userLevels {
		details, err := s.GetUserActivityDetails(ctx, level.UserID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get user details", zap.String("user_id", level.UserID.String()), zap.Error(err))
			continue
		}
		results = append(results, *details)
	}

	return results, int(total), nil
}

// GetAllTiersWithStats retrieves all tiers with user statistics
func (s *AdminActivityService) GetAllTiersWithStats(ctx context.Context) ([]TierWithStats, error) {
	tiers, err := s.tierService.GetAllTiers(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get tiers: %w", err)
	}

	var results []TierWithStats
	for _, tier := range tiers {
		var userCount int64
		if err := global.GVA_DB.Model(&model.UserActivityLevel{}).
			Where("activity_tier_id = ?", tier.ID).
			Count(&userCount).Error; err != nil {
			continue
		}

		results = append(results, TierWithStats{
			Tier:      tier,
			UserCount: int(userCount),
		})
	}

	return results, nil
}
