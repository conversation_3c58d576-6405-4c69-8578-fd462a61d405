package activity

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity"
)

// ActivityTierServiceInterface defines the interface for activity tier service
type ActivityTierServiceInterface interface {
	// Tier Management
	GetAllTiers(ctx context.Context) ([]model.ActivityTier, error)
	GetTierByLevel(ctx context.Context, level int) (*model.ActivityTier, error)
	CreateDefaultTiers(ctx context.Context) error

	// User Activity Level Management
	GetUserActivityLevel(ctx context.Context, userID uuid.UUID) (*model.UserActivityLevel, error)
	InitializeUserActivityLevel(ctx context.Context, userID uuid.UUID) (*model.UserActivityLevel, error)
	UpdateUserPoints(ctx context.Context, userID uuid.UUID, points int, source, description string, referenceID *uuid.UUID, referenceType string) error
	CalculateUserTier(ctx context.Context, userID uuid.UUID) (*model.ActivityTier, error)
	UpgradeUserTier(ctx context.Context, userID uuid.UUID, newTierID uint, triggerType, triggerData string) error

	// Cashback Calculations
	GetUserCashbackPercentage(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
	CalculateCashbackAmount(ctx context.Context, userID uuid.UUID, tradingFeeAmount decimal.Decimal) (decimal.Decimal, error)

	// Statistics and Analytics
	GetTierDistribution(ctx context.Context) (map[uint]int, error)
	GetUserTierHistory(ctx context.Context, userID uuid.UUID) ([]model.ActivityTierUpgrade, error)
}

// ActivityTierService implements the activity tier service interface
type ActivityTierService struct {
	tierRepo activity.ActivityTierRepositoryInterface
}

// NewActivityTierService creates a new activity tier service
func NewActivityTierService(tierRepo activity.ActivityTierRepositoryInterface) ActivityTierServiceInterface {
	return &ActivityTierService{
		tierRepo: tierRepo,
	}
}

// GetAllTiers retrieves all activity tiers
func (s *ActivityTierService) GetAllTiers(ctx context.Context) ([]model.ActivityTier, error) {
	return s.tierRepo.GetAllTiers(ctx)
}

// GetTierByLevel retrieves a tier by level
func (s *ActivityTierService) GetTierByLevel(ctx context.Context, level int) (*model.ActivityTier, error) {
	return s.tierRepo.GetTierByLevel(ctx, level)
}

// CreateDefaultTiers creates the default activity tier structure
func (s *ActivityTierService) CreateDefaultTiers(ctx context.Context) error {
	defaultTiers := []model.ActivityTier{
		{
			ID:                 1,
			Name:               "Bronze",
			Level:              1,
			MinPoints:          0,
			MaxPoints:          intPtr(999),
			Color:              "#CD7F32",
			Icon:               "bronze-medal",
			Description:        "Welcome tier for new users",
			CashbackPercentage: decimal.NewFromFloat(0.01), // 1%
			Benefits:           `{"features": ["Basic cashback", "Daily tasks"]}`,
		},
		{
			ID:                 2,
			Name:               "Silver",
			Level:              2,
			MinPoints:          1000,
			MaxPoints:          intPtr(4999),
			Color:              "#C0C0C0",
			Icon:               "silver-medal",
			Description:        "Active user tier with enhanced benefits",
			CashbackPercentage: decimal.NewFromFloat(0.02), // 2%
			Benefits:           `{"features": ["Enhanced cashback", "Weekly bonuses", "Priority support"]}`,
		},
		{
			ID:                 3,
			Name:               "Gold",
			Level:              3,
			MinPoints:          5000,
			MaxPoints:          intPtr(14999),
			Color:              "#FFD700",
			Icon:               "gold-medal",
			Description:        "Premium tier for dedicated traders",
			CashbackPercentage: decimal.NewFromFloat(0.03), // 3%
			Benefits:           `{"features": ["Premium cashback", "Exclusive tasks", "Monthly rewards", "VIP support"]}`,
		},
		{
			ID:                 4,
			Name:               "Platinum",
			Level:              4,
			MinPoints:          15000,
			MaxPoints:          intPtr(49999),
			Color:              "#E5E4E2",
			Icon:               "platinum-medal",
			Description:        "Elite tier for high-volume traders",
			CashbackPercentage: decimal.NewFromFloat(0.04), // 4%
			Benefits:           `{"features": ["Elite cashback", "Special events", "Personal account manager", "Advanced analytics"]}`,
		},
		{
			ID:                 5,
			Name:               "Diamond",
			Level:              5,
			MinPoints:          50000,
			MaxPoints:          nil, // No upper limit
			Color:              "#B9F2FF",
			Icon:               "diamond",
			Description:        "Ultimate tier for the most active users",
			CashbackPercentage: decimal.NewFromFloat(0.05), // 5%
			Benefits:           `{"features": ["Maximum cashback", "Exclusive access", "Custom rewards", "Direct line to team"]}`,
		},
	}

	for _, tier := range defaultTiers {
		// Check if tier already exists
		existing, err := s.tierRepo.GetTierByLevel(ctx, tier.Level)
		if err == nil && existing != nil {
			global.GVA_LOG.Info("Tier already exists, skipping", zap.Int("level", tier.Level))
			continue
		}

		if err := s.tierRepo.CreateTier(ctx, &tier); err != nil {
			return fmt.Errorf("failed to create tier %s: %w", tier.Name, err)
		}
		global.GVA_LOG.Info("Created default tier", zap.String("name", tier.Name), zap.Int("level", tier.Level))
	}

	return nil
}

// GetUserActivityLevel retrieves user's activity level, creating if not exists
func (s *ActivityTierService) GetUserActivityLevel(ctx context.Context, userID uuid.UUID) (*model.UserActivityLevel, error) {
	level, err := s.tierRepo.GetUserActivityLevel(ctx, userID)
	if err != nil {
		// If not found, initialize new level
		return s.InitializeUserActivityLevel(ctx, userID)
	}
	return level, nil
}

// InitializeUserActivityLevel creates a new user activity level with default tier
func (s *ActivityTierService) InitializeUserActivityLevel(ctx context.Context, userID uuid.UUID) (*model.UserActivityLevel, error) {
	// Get the default tier (Bronze - Level 1)
	defaultTier, err := s.tierRepo.GetTierByLevel(ctx, 1)
	if err != nil {
		return nil, fmt.Errorf("failed to get default tier: %w", err)
	}

	level := &model.UserActivityLevel{
		UserID:               userID,
		ActivityTierID:       defaultTier.ID,
		TotalPoints:          0,
		CurrentPoints:        0,
		TotalTradingVolume:   decimal.Zero,
		MonthlyTradingVolume: decimal.Zero,
		MonthlyActiveDays:    0,
		TotalCashbackEarned:  decimal.Zero,
		TierUpgradeCount:     0,
	}

	if err := s.tierRepo.CreateUserActivityLevel(ctx, level); err != nil {
		return nil, fmt.Errorf("failed to create user activity level: %w", err)
	}

	global.GVA_LOG.Info("Initialized user activity level", zap.String("user_id", userID.String()), zap.Uint("tier_id", defaultTier.ID))
	return level, nil
}

// UpdateUserPoints updates user's points and handles tier upgrades
func (s *ActivityTierService) UpdateUserPoints(ctx context.Context, userID uuid.UUID, points int, source, description string, referenceID *uuid.UUID, referenceType string) error {
	// Get user's current activity level
	userLevel, err := s.GetUserActivityLevel(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user activity level: %w", err)
	}

	// Create point transaction record
	transaction := &model.UserPointTransaction{
		UserID:        userID,
		Points:        points,
		Type:          source,
		Source:        source,
		Description:   description,
		ReferenceID:   referenceID,
		ReferenceType: referenceType,
	}

	if err := s.tierRepo.CreatePointTransaction(ctx, transaction); err != nil {
		return fmt.Errorf("failed to create point transaction: %w", err)
	}

	// Update user's points
	userLevel.TotalPoints += points
	userLevel.CurrentPoints += points

	// Check for tier upgrade
	newTier, err := s.CalculateUserTier(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to calculate user tier: %w", err)
	}

	// If tier changed, upgrade user
	if newTier.ID != userLevel.ActivityTierID {
		if err := s.UpgradeUserTier(ctx, userID, newTier.ID, "POINTS", fmt.Sprintf(`{"points": %d, "source": "%s"}`, points, source)); err != nil {
			return fmt.Errorf("failed to upgrade user tier: %w", err)
		}
		userLevel.ActivityTierID = newTier.ID
		userLevel.TierUpgradeCount++
		now := time.Now()
		userLevel.LastTierUpgrade = &now
	}

	// Save updated user level
	if err := s.tierRepo.UpdateUserActivityLevel(ctx, userLevel); err != nil {
		return fmt.Errorf("failed to update user activity level: %w", err)
	}

	global.GVA_LOG.Info("Updated user points",
		zap.String("user_id", userID.String()),
		zap.Int("points", points),
		zap.String("source", source),
		zap.Uint("tier_id", userLevel.ActivityTierID))

	return nil
}

// CalculateUserTier determines the appropriate tier for a user based on their points
func (s *ActivityTierService) CalculateUserTier(ctx context.Context, userID uuid.UUID) (*model.ActivityTier, error) {
	// Get user's current points
	userLevel, err := s.tierRepo.GetUserActivityLevel(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user activity level: %w", err)
	}

	// Get all tiers
	tiers, err := s.tierRepo.GetAllTiers(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get all tiers: %w", err)
	}

	// Find the highest tier the user qualifies for
	var targetTier *model.ActivityTier
	for i := len(tiers) - 1; i >= 0; i-- {
		tier := &tiers[i]
		if userLevel.CurrentPoints >= tier.MinPoints {
			if tier.MaxPoints == nil || userLevel.CurrentPoints <= *tier.MaxPoints {
				targetTier = tier
				break
			}
		}
	}

	// If no tier found, return the lowest tier
	if targetTier == nil {
		targetTier = &tiers[0]
	}

	return targetTier, nil
}

// UpgradeUserTier upgrades a user to a new tier
func (s *ActivityTierService) UpgradeUserTier(ctx context.Context, userID uuid.UUID, newTierID uint, triggerType, triggerData string) error {
	// Get current user level
	userLevel, err := s.tierRepo.GetUserActivityLevel(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user activity level: %w", err)
	}

	// Create tier upgrade record
	upgrade := &model.ActivityTierUpgrade{
		UserID:          userID,
		FromTierID:      userLevel.ActivityTierID,
		ToTierID:        newTierID,
		PointsAtUpgrade: userLevel.CurrentPoints,
		TriggerType:     triggerType,
		TriggerData:     triggerData,
	}

	if err := s.tierRepo.CreateTierUpgrade(ctx, upgrade); err != nil {
		return fmt.Errorf("failed to create tier upgrade: %w", err)
	}

	global.GVA_LOG.Info("User tier upgraded",
		zap.String("user_id", userID.String()),
		zap.Uint("from_tier", userLevel.ActivityTierID),
		zap.Uint("to_tier", newTierID),
		zap.String("trigger", triggerType))

	return nil
}

// GetUserCashbackPercentage gets the cashback percentage for a user based on their tier
func (s *ActivityTierService) GetUserCashbackPercentage(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	userLevel, err := s.GetUserActivityLevel(ctx, userID)
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get user activity level: %w", err)
	}

	tier, err := s.tierRepo.GetTierByID(ctx, userLevel.ActivityTierID)
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get tier: %w", err)
	}

	return tier.CashbackPercentage, nil
}

// CalculateCashbackAmount calculates the cashback amount for a user based on trading fees
func (s *ActivityTierService) CalculateCashbackAmount(ctx context.Context, userID uuid.UUID, tradingFeeAmount decimal.Decimal) (decimal.Decimal, error) {
	cashbackPercentage, err := s.GetUserCashbackPercentage(ctx, userID)
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get cashback percentage: %w", err)
	}

	cashbackAmount := tradingFeeAmount.Mul(cashbackPercentage)
	return cashbackAmount, nil
}

// GetTierDistribution returns the distribution of users across tiers
func (s *ActivityTierService) GetTierDistribution(ctx context.Context) (map[uint]int, error) {
	tiers, err := s.tierRepo.GetAllTiers(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get all tiers: %w", err)
	}

	distribution := make(map[uint]int)
	for _, tier := range tiers {
		users, err := s.tierRepo.GetUsersByTier(ctx, tier.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to get users by tier: %w", err)
		}
		distribution[tier.ID] = len(users)
	}

	return distribution, nil
}

// GetUserTierHistory returns the tier upgrade history for a user
func (s *ActivityTierService) GetUserTierHistory(ctx context.Context, userID uuid.UUID) ([]model.ActivityTierUpgrade, error) {
	return s.tierRepo.GetUserTierUpgrades(ctx, userID)
}

// Helper function to create int pointer
func intPtr(i int) *int {
	return &i
}
