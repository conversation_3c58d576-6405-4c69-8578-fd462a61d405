package resolvers

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity"
	activityService "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity"
)

// ActivityResolver handles activity-related GraphQL operations
type ActivityResolver struct {
	tierService activityService.ActivityTierServiceInterface
	taskService activityService.ActivityTaskServiceInterface
	tierRepo    activity.ActivityTierRepositoryInterface
	taskRepo    activity.ActivityTaskRepositoryInterface
	rewardRepo  activity.ActivityRewardRepositoryInterface
}

// NewActivityResolver creates a new activity resolver
func NewActivityResolver() *ActivityResolver {
	tierRepo := activity.NewActivityTierRepository()
	taskRepo := activity.NewActivityTaskRepository()
	rewardRepo := activity.NewActivityRewardRepository()

	tierService := activityService.NewActivityTierService(tierRepo)
	taskService := activityService.NewActivityTaskService(taskRepo, tierService)

	return &ActivityResolver{
		tierService: tierService,
		taskService: taskService,
		tierRepo:    tierRepo,
		taskRepo:    taskRepo,
		rewardRepo:  rewardRepo,
	}
}

// GetActivityTiers retrieves all activity tiers
func (r *ActivityResolver) GetActivityTiers(ctx context.Context) (*gql_model.ActivityTierResponse, error) {
	tiers, err := r.tierService.GetAllTiers(ctx)
	if err != nil {
		return &gql_model.ActivityTierResponse{
			Success: false,
			Message: stringPtr("Failed to get activity tiers"),
		}, nil
	}

	// Convert to GraphQL model
	var gqlTiers []*gql_model.ActivityTier
	for _, tier := range tiers {
		gqlTier := &gql_model.ActivityTier{
			ID:                 int(tier.ID),
			Name:               tier.Name,
			Level:              tier.Level,
			MinPoints:          tier.MinPoints,
			MaxPoints:          tier.MaxPoints,
			Color:              tier.Color,
			Icon:               stringPtr(tier.Icon),
			Description:        stringPtr(tier.Description),
			CashbackPercentage: float64(tier.CashbackPercentage.InexactFloat64()),
			Benefits:           stringPtr(tier.Benefits),
			CreatedAt:          tier.CreatedAt,
			UpdatedAt:          tier.UpdatedAt,
		}
		gqlTiers = append(gqlTiers, gqlTier)
	}

	return &gql_model.ActivityTierResponse{
		Tiers:   gqlTiers,
		Success: true,
		Message: stringPtr("Activity tiers retrieved successfully"),
	}, nil
}

// GetUserActivityLevel retrieves user's activity level
func (r *ActivityResolver) GetUserActivityLevel(ctx context.Context) (*gql_model.UserActivityLevelResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.UserActivityLevelResponse{
			Success: false,
			Message: stringPtr("User not authenticated"),
		}, nil
	}

	userLevel, err := r.tierService.GetUserActivityLevel(ctx, userID)
	if err != nil {
		return &gql_model.UserActivityLevelResponse{
			Success: false,
			Message: stringPtr("Failed to get user activity level"),
		}, nil
	}

	// Convert to GraphQL model
	var previousTierID *int
	if userLevel.PreviousTierID != nil {
		id := int(*userLevel.PreviousTierID)
		previousTierID = &id
	}

	gqlUserLevel := &gql_model.UserActivityLevel{
		ID:                   userLevel.ID.String(),
		UserID:               userLevel.UserID.String(),
		TotalPoints:          userLevel.TotalPoints,
		CurrentPoints:        userLevel.CurrentPoints,
		TotalTradingVolume:   userLevel.TotalTradingVolume.InexactFloat64(),
		MonthlyTradingVolume: userLevel.MonthlyTradingVolume.InexactFloat64(),
		MonthlyActiveDays:    userLevel.MonthlyActiveDays,
		TotalCashbackEarned:  userLevel.TotalCashbackEarned.InexactFloat64(),
		LastTierUpgrade:      userLevel.LastTierUpgrade,
		TierUpgradeCount:     userLevel.TierUpgradeCount,
		PreviousTierID:       previousTierID,
		CreatedAt:            userLevel.CreatedAt,
		UpdatedAt:            userLevel.UpdatedAt,
	}

	// Add activity tier information
	if userLevel.ActivityTier.ID != 0 {
		gqlUserLevel.ActivityTier = &gql_model.ActivityTier{
			ID:                 int(userLevel.ActivityTier.ID),
			Name:               userLevel.ActivityTier.Name,
			Level:              userLevel.ActivityTier.Level,
			MinPoints:          userLevel.ActivityTier.MinPoints,
			MaxPoints:          userLevel.ActivityTier.MaxPoints,
			Color:              userLevel.ActivityTier.Color,
			Icon:               stringPtr(userLevel.ActivityTier.Icon),
			Description:        stringPtr(userLevel.ActivityTier.Description),
			CashbackPercentage: userLevel.ActivityTier.CashbackPercentage.InexactFloat64(),
			Benefits:           stringPtr(userLevel.ActivityTier.Benefits),
			CreatedAt:          userLevel.ActivityTier.CreatedAt,
			UpdatedAt:          userLevel.ActivityTier.UpdatedAt,
		}
	}

	return &gql_model.UserActivityLevelResponse{
		UserLevel: gqlUserLevel,
		Success:   true,
		Message:   stringPtr("User activity level retrieved successfully"),
	}, nil
}

// GetUserTasks retrieves user's tasks
func (r *ActivityResolver) GetUserTasks(ctx context.Context, input gql_model.GetUserTasksInput) (*gql_model.UserTasksResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.UserTasksResponse{
			Success: false,
			Message: stringPtr("User not authenticated"),
		}, nil
	}

	var userTasks []model.UserTaskProgress
	var err error

	if input.Category != nil {
		// Convert GraphQL enum to model enum
		category := convertTaskCategory(*input.Category)
		userTasks, err = r.taskService.GetUserTasksByCategory(ctx, userID, category)
	} else {
		userTasks, err = r.taskService.GetUserTaskProgress(ctx, userID)
	}

	if err != nil {
		return &gql_model.UserTasksResponse{
			Success: false,
			Message: stringPtr("Failed to get user tasks"),
		}, nil
	}

	// Convert to GraphQL model
	var gqlTasks []*gql_model.UserTaskProgress
	for _, task := range userTasks {
		gqlTask := convertUserTaskProgressToGQL(task)
		gqlTasks = append(gqlTasks, gqlTask)
	}

	return &gql_model.UserTasksResponse{
		Tasks:   gqlTasks,
		Success: true,
		Message: stringPtr("User tasks retrieved successfully"),
	}, nil
}

// ClaimTaskReward claims reward for a completed task
func (r *ActivityResolver) ClaimTaskReward(ctx context.Context, input gql_model.ClaimTaskRewardInput) (*gql_model.TaskRewardClaimResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.TaskRewardClaimResponse{
			Success: false,
			Message: "User not authenticated",
		}, nil
	}

	taskID, err := uuid.Parse(input.TaskID)
	if err != nil {
		return &gql_model.TaskRewardClaimResponse{
			Success: false,
			Message: "Invalid task ID",
		}, nil
	}

	// Get task progress before claiming to get reward amounts
	progress, err := r.taskRepo.GetUserTaskProgress(ctx, userID, taskID)
	if err != nil {
		return &gql_model.TaskRewardClaimResponse{
			Success: false,
			Message: "Task not found",
		}, nil
	}

	if err := r.taskService.ClaimTaskReward(ctx, userID, taskID); err != nil {
		return &gql_model.TaskRewardClaimResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to claim task reward: %v", err),
		}, nil
	}

	return &gql_model.TaskRewardClaimResponse{
		Success:         true,
		Message:         "Task reward claimed successfully",
		PointsAwarded:   intPtr(progress.Task.PointReward),
		CashbackAwarded: floatPtr(progress.Task.CashbackReward.InexactFloat64()),
	}, nil
}

// RefreshUserTasks refreshes user's task list
func (r *ActivityResolver) RefreshUserTasks(ctx context.Context, input gql_model.RefreshUserTasksInput) (*gql_model.UserTasksResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.UserTasksResponse{
			Success: false,
			Message: stringPtr("User not authenticated"),
		}, nil
	}

	if err := r.taskService.RefreshUserTasks(ctx, userID); err != nil {
		return &gql_model.UserTasksResponse{
			Success: false,
			Message: stringPtr("Failed to refresh user tasks"),
		}, nil
	}

	// Get updated tasks
	userTasks, err := r.taskService.GetUserTaskProgress(ctx, userID)
	if err != nil {
		return &gql_model.UserTasksResponse{
			Success: false,
			Message: stringPtr("Failed to get updated user tasks"),
		}, nil
	}

	// Convert to GraphQL model
	var gqlTasks []*gql_model.UserTaskProgress
	for _, task := range userTasks {
		gqlTask := convertUserTaskProgressToGQL(task)
		gqlTasks = append(gqlTasks, gqlTask)
	}

	return &gql_model.UserTasksResponse{
		Tasks:   gqlTasks,
		Success: true,
		Message: stringPtr("User tasks refreshed successfully"),
	}, nil
}

// CompleteTask manually completes a task (for manual verification tasks)
func (r *ActivityResolver) CompleteTask(ctx context.Context, input gql_model.CompleteTaskInput) (*gql_model.TaskRewardClaimResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.TaskRewardClaimResponse{
			Success: false,
			Message: "User not authenticated",
		}, nil
	}

	taskID, err := uuid.Parse(input.TaskID)
	if err != nil {
		return &gql_model.TaskRewardClaimResponse{
			Success: false,
			Message: "Invalid task ID",
		}, nil
	}

	var referenceID *uuid.UUID
	if input.ReferenceID != nil {
		refID, err := uuid.Parse(*input.ReferenceID)
		if err == nil {
			referenceID = &refID
		}
	}

	triggerSource := ""
	if input.TriggerSource != nil {
		triggerSource = *input.TriggerSource
	}

	if err := r.taskService.CompleteTask(ctx, userID, taskID, input.TriggerType, triggerSource, referenceID); err != nil {
		return &gql_model.TaskRewardClaimResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to complete task: %v", err),
		}, nil
	}

	return &gql_model.TaskRewardClaimResponse{
		Success: true,
		Message: "Task completed successfully",
	}, nil
}

// GetUserActivityStats retrieves user's activity statistics
func (r *ActivityResolver) GetUserActivityStats(ctx context.Context) (*gql_model.UserActivityStatsResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.UserActivityStatsResponse{
			Success: false,
			Message: stringPtr("User not authenticated"),
		}, nil
	}

	stats, err := r.rewardRepo.GetUserActivityStats(ctx, userID)
	if err != nil {
		return &gql_model.UserActivityStatsResponse{
			Success: false,
			Message: stringPtr("Failed to get user activity stats"),
		}, nil
	}

	// Convert to GraphQL model
	gqlStats := &gql_model.UserActivityStats{
		ID:                    stats.ID.String(),
		UserID:                stats.UserID.String(),
		DailyTasksCompleted:   stats.DailyTasksCompleted,
		DailyPointsEarned:     stats.DailyPointsEarned,
		DailyCashbackEarned:   stats.DailyCashbackEarned.InexactFloat64(),
		DailyTradingVolume:    stats.DailyTradingVolume.InexactFloat64(),
		DailyLastReset:        stats.DailyLastReset,
		WeeklyTasksCompleted:  stats.WeeklyTasksCompleted,
		WeeklyPointsEarned:    stats.WeeklyPointsEarned,
		WeeklyCashbackEarned:  stats.WeeklyCashbackEarned.InexactFloat64(),
		WeeklyTradingVolume:   stats.WeeklyTradingVolume.InexactFloat64(),
		WeeklyLastReset:       stats.WeeklyLastReset,
		MonthlyTasksCompleted: stats.MonthlyTasksCompleted,
		MonthlyPointsEarned:   stats.MonthlyPointsEarned,
		MonthlyCashbackEarned: stats.MonthlyCashbackEarned.InexactFloat64(),
		MonthlyTradingVolume:  stats.MonthlyTradingVolume.InexactFloat64(),
		MonthlyActiveDays:     stats.MonthlyActiveDays,
		MonthlyLastReset:      stats.MonthlyLastReset,
		TotalTasksCompleted:   stats.TotalTasksCompleted,
		TotalPointsEarned:     stats.TotalPointsEarned,
		TotalCashbackEarned:   stats.TotalCashbackEarned.InexactFloat64(),
		TotalTradingVolume:    stats.TotalTradingVolume.InexactFloat64(),
		TotalActiveDays:       stats.TotalActiveDays,
		CurrentLoginStreak:    stats.CurrentLoginStreak,
		BestLoginStreak:       stats.BestLoginStreak,
		CurrentTradingStreak:  stats.CurrentTradingStreak,
		BestTradingStreak:     stats.BestTradingStreak,
		LastActivityAt:        stats.LastActivityAt,
		LastTradingAt:         stats.LastTradingAt,
		CreatedAt:             stats.CreatedAt,
		UpdatedAt:             stats.UpdatedAt,
	}

	return &gql_model.UserActivityStatsResponse{
		Stats:   gqlStats,
		Success: true,
		Message: stringPtr("User activity stats retrieved successfully"),
	}, nil
}

// Helper functions

func stringPtr(s string) *string {
	return &s
}

func intPtr(i int) *int {
	return &i
}

func floatPtr(f float64) *float64 {
	return &f
}

func convertTaskCategory(gqlCategory gql_model.TaskCategory) model.TaskCategory {
	switch gqlCategory {
	case gql_model.TaskCategoryDailyTasks:
		return model.TaskCategoryDaily
	case gql_model.TaskCategoryCommunityTasks:
		return model.TaskCategoryCommunity
	case gql_model.TaskCategoryTradingTasks:
		return model.TaskCategoryTrading
	default:
		return model.TaskCategoryDaily
	}
}

func convertUserTaskProgressToGQL(task model.UserTaskProgress) *gql_model.UserTaskProgress {
	gqlTask := &gql_model.UserTaskProgress{
		ID:             task.ID.String(),
		UserID:         task.UserID.String(),
		Status:         convertUserTaskStatus(task.Status),
		CurrentCount:   task.CurrentCount,
		CurrentVolume:  task.CurrentVolume.InexactFloat64(),
		CompletedCount: task.CompletedCount,
		CurrentStreak:  task.CurrentStreak,
		BestStreak:     task.BestStreak,
		LastStreakAt:   task.LastStreakAt,
		StartedAt:      task.StartedAt,
		CompletedAt:    task.CompletedAt,
		ClaimedAt:      task.ClaimedAt,
		ExpiresAt:      task.ExpiresAt,
		LastUpdateAt:   task.LastUpdateAt,
		PeriodStart:    task.PeriodStart,
		PeriodEnd:      task.PeriodEnd,
		PointsEarned:   task.PointsEarned,
		CashbackEarned: task.CashbackEarned.InexactFloat64(),
		TotalRewardUsd: task.TotalRewardUSD.InexactFloat64(),
		Metadata:       stringPtr(task.Metadata),
		CreatedAt:      task.CreatedAt,
		UpdatedAt:      task.UpdatedAt,
	}

	// Add task information
	if task.Task.ID != uuid.Nil {
		gqlTask.Task = &gql_model.ActivityTask{
			ID:              task.Task.ID.String(),
			Name:            task.Task.Name,
			Description:     stringPtr(task.Task.Description),
			Type:            convertTaskType(task.Task.Type),
			Category:        convertTaskCategoryToGQL(task.Task.Category),
			Status:          convertTaskStatusToGQL(task.Task.Status),
			RequiredCount:   task.Task.RequiredCount,
			RequiredVolume:  task.Task.RequiredVolume.InexactFloat64(),
			MinVolume:       task.Task.MinVolume.InexactFloat64(),
			TimeWindowHours: task.Task.TimeWindowHours,
			ValidFrom:       task.Task.ValidFrom,
			ValidUntil:      task.Task.ValidUntil,
			PointReward:     task.Task.PointReward,
			CashbackReward:  task.Task.CashbackReward.InexactFloat64(),
			Config:          stringPtr(task.Task.Config),
			Metadata:        stringPtr(task.Task.Metadata),
			Icon:            stringPtr(task.Task.Icon),
			Color:           stringPtr(task.Task.Color),
			Priority:        task.Task.Priority,
			IsHighlight:     task.Task.IsHighlight,
			CreatedAt:       task.Task.CreatedAt,
			UpdatedAt:       task.Task.UpdatedAt,
		}
	}

	return gqlTask
}

func convertUserTaskStatus(status model.UserTaskStatus) gql_model.UserTaskStatus {
	switch status {
	case model.UserTaskStatusNotStarted:
		return gql_model.UserTaskStatusNotStarted
	case model.UserTaskStatusInProgress:
		return gql_model.UserTaskStatusInProgress
	case model.UserTaskStatusCompleted:
		return gql_model.UserTaskStatusCompleted
	case model.UserTaskStatusClaimed:
		return gql_model.UserTaskStatusClaimed
	case model.UserTaskStatusExpired:
		return gql_model.UserTaskStatusExpired
	default:
		return gql_model.UserTaskStatusNotStarted
	}
}

func convertTaskType(taskType model.TaskType) gql_model.TaskType {
	switch taskType {
	case model.TaskTypeDaily:
		return gql_model.TaskTypeDaily
	case model.TaskTypeCommunity:
		return gql_model.TaskTypeCommunity
	case model.TaskTypeTrading:
		return gql_model.TaskTypeTrading
	case model.TaskTypeOneTime:
		return gql_model.TaskTypeOneTime
	case model.TaskTypeUnlimited:
		return gql_model.TaskTypeUnlimited
	case model.TaskTypeProgress:
		return gql_model.TaskTypeProgress
	case model.TaskTypeManualUpdate:
		return gql_model.TaskTypeManualUpdate
	default:
		return gql_model.TaskTypeDaily
	}
}

func convertTaskCategoryToGQL(category model.TaskCategory) gql_model.TaskCategory {
	switch category {
	case model.TaskCategoryDaily:
		return gql_model.TaskCategoryDailyTasks
	case model.TaskCategoryCommunity:
		return gql_model.TaskCategoryCommunityTasks
	case model.TaskCategoryTrading:
		return gql_model.TaskCategoryTradingTasks
	default:
		return gql_model.TaskCategoryDailyTasks
	}
}

func convertTaskStatusToGQL(status model.TaskStatus) gql_model.TaskStatus {
	switch status {
	case model.TaskStatusActive:
		return gql_model.TaskStatusActive
	case model.TaskStatusInactive:
		return gql_model.TaskStatusInactive
	case model.TaskStatusArchived:
		return gql_model.TaskStatusArchived
	default:
		return gql_model.TaskStatusActive
	}
}
