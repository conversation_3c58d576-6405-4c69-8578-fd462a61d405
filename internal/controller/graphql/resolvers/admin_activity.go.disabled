package resolvers

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/middleware"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity"
	activityService "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity"
)

// AdminActivityResolver handles admin activity-related GraphQL operations
type AdminActivityResolver struct {
	adminService activityService.AdminActivityServiceInterface
	tierService  activityService.ActivityTierServiceInterface
	taskService  activityService.ActivityTaskServiceInterface
}

// NewAdminActivityResolver creates a new admin activity resolver
func NewAdminActivityResolver() *AdminActivityResolver {
	tierRepo := activity.NewActivityTierRepository()
	taskRepo := activity.NewActivityTaskRepository()
	rewardRepo := activity.NewActivityRewardRepository()

	tierService := activityService.NewActivityTierService(tierRepo)
	taskService := activityService.NewActivityTaskService(taskRepo, tierService)
	integrationService := activityService.NewActivityIntegrationService(
		tierService, taskService, rewardRepo, taskRepo, tierRepo)

	adminService := activityService.NewAdminActivityService(
		tierService, taskService, integrationService, tierRepo, taskRepo, rewardRepo)

	return &AdminActivityResolver{
		adminService: adminService,
		tierService:  tierService,
		taskService:  taskService,
	}
}

// AdminActivityDashboard retrieves dashboard statistics
func (r *AdminActivityResolver) AdminActivityDashboard(ctx context.Context) (*gql_model.AdminActivityDashboard, error) {
	// Check admin authentication
	if !IsAdminUser(ctx) {
		return nil, fmt.Errorf("admin access required")
	}

	stats, err := r.adminService.GetDashboardStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get dashboard stats: %w", err)
	}

	// Convert to GraphQL model
	var tierDistribution []*gql_model.TierDistribution
	for _, dist := range stats.TierDistribution {
		tierDistribution = append(tierDistribution, &gql_model.TierDistribution{
			Tier: &gql_model.ActivityTier{
				ID:                 int(dist.Tier.ID),
				Name:               dist.Tier.Name,
				Level:              dist.Tier.Level,
				MinPoints:          dist.Tier.MinPoints,
				MaxPoints:          dist.Tier.MaxPoints,
				Color:              dist.Tier.Color,
				Icon:               stringPtr(dist.Tier.Icon),
				Description:        stringPtr(dist.Tier.Description),
				CashbackPercentage: dist.Tier.CashbackPercentage.InexactFloat64(),
				Benefits:           stringPtr(dist.Tier.Benefits),
				CreatedAt:          dist.Tier.CreatedAt,
				UpdatedAt:          dist.Tier.UpdatedAt,
			},
			UserCount:  dist.UserCount,
			Percentage: dist.Percentage,
		})
	}

	var taskCompletionStats []*gql_model.TaskCompletionStat
	for _, stat := range stats.TaskCompletionStats {
		taskCompletionStats = append(taskCompletionStats, &gql_model.TaskCompletionStat{
			Task: &gql_model.ActivityTask{
				ID:              stat.Task.ID.String(),
				Name:            stat.Task.Name,
				Description:     stringPtr(stat.Task.Description),
				Type:            convertTaskType(stat.Task.Type),
				Category:        convertTaskCategoryToGQL(stat.Task.Category),
				Status:          convertTaskStatusToGQL(stat.Task.Status),
				RequiredCount:   stat.Task.RequiredCount,
				RequiredVolume:  stat.Task.RequiredVolume.InexactFloat64(),
				MinVolume:       stat.Task.MinVolume.InexactFloat64(),
				TimeWindowHours: stat.Task.TimeWindowHours,
				ValidFrom:       stat.Task.ValidFrom,
				ValidUntil:      stat.Task.ValidUntil,
				PointReward:     stat.Task.PointReward,
				CashbackReward:  stat.Task.CashbackReward.InexactFloat64(),
				Config:          stringPtr(stat.Task.Config),
				Metadata:        stringPtr(stat.Task.Metadata),
				Icon:            stringPtr(stat.Task.Icon),
				Color:           stringPtr(stat.Task.Color),
				Priority:        stat.Task.Priority,
				IsHighlight:     stat.Task.IsHighlight,
				CreatedAt:       stat.Task.CreatedAt,
				UpdatedAt:       stat.Task.UpdatedAt,
			},
			CompletionCount:       stat.CompletionCount,
			CompletionRate:        stat.CompletionRate,
			AverageTimeToComplete: stat.AverageTimeToComplete,
		})
	}

	var recentActivities []*gql_model.RecentActivity
	for _, activity := range stats.RecentActivities {
		recentActivities = append(recentActivities, &gql_model.RecentActivity{
			ID:            activity.ID.String(),
			UserID:        activity.UserID.String(),
			UserName:      stringPtr(activity.UserName),
			ActivityType:  activity.ActivityType,
			Description:   activity.Description,
			PointsAwarded: intPtr(activity.PointsAwarded),
			Timestamp:     activity.Timestamp,
		})
	}

	return &gql_model.AdminActivityDashboard{
		TotalUsers:               stats.TotalUsers,
		TotalActiveTasks:         stats.TotalActiveTasks,
		TotalPointsAwarded:       stats.TotalPointsAwarded,
		TotalCashbackDistributed: stats.TotalCashbackDistributed.InexactFloat64(),
		TierDistribution:         tierDistribution,
		TaskCompletionStats:      taskCompletionStats,
		RecentActivities:         recentActivities,
		SystemHealth: &gql_model.SystemHealth{
			Status:          stats.SystemHealth.Status,
			LastProcessedAt: stats.SystemHealth.LastProcessedAt,
			PendingTasks:    stats.SystemHealth.PendingTasks,
			ErrorCount:      stats.SystemHealth.ErrorCount,
			Uptime:          stats.SystemHealth.Uptime,
		},
	}, nil
}

// AdminActivityTiers retrieves all activity tiers for admin
func (r *AdminActivityResolver) AdminActivityTiers(ctx context.Context) ([]*gql_model.ActivityTier, error) {
	if !IsAdminUser(ctx) {
		return nil, fmt.Errorf("admin access required")
	}

	tiers, err := r.tierService.GetAllTiers(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get tiers: %w", err)
	}

	var gqlTiers []*gql_model.ActivityTier
	for _, tier := range tiers {
		gqlTiers = append(gqlTiers, &gql_model.ActivityTier{
			ID:                 int(tier.ID),
			Name:               tier.Name,
			Level:              tier.Level,
			MinPoints:          tier.MinPoints,
			MaxPoints:          tier.MaxPoints,
			Color:              tier.Color,
			Icon:               stringPtr(tier.Icon),
			Description:        stringPtr(tier.Description),
			CashbackPercentage: tier.CashbackPercentage.InexactFloat64(),
			Benefits:           stringPtr(tier.Benefits),
			CreatedAt:          tier.CreatedAt,
			UpdatedAt:          tier.UpdatedAt,
		})
	}

	return gqlTiers, nil
}

// AdminCreateActivityTier creates a new activity tier
func (r *AdminActivityResolver) AdminCreateActivityTier(ctx context.Context, input gql_model.CreateActivityTierInput) (*gql_model.AdminOperationResult, error) {
	if !IsAdminUser(ctx) {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: "Admin access required",
		}, nil
	}

	tier := &model.ActivityTier{
		Name:               input.Name,
		Level:              input.Level,
		MinPoints:          input.MinPoints,
		MaxPoints:          input.MaxPoints,
		Color:              input.Color,
		Icon:               stringValue(input.Icon),
		Description:        stringValue(input.Description),
		CashbackPercentage: decimal.NewFromFloat(input.CashbackPercentage),
		Benefits:           stringValue(input.Benefits),
	}

	if err := r.adminService.CreateActivityTier(ctx, tier); err != nil {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: fmt.Sprintf("Failed to create tier: %v", err),
		}, nil
	}

	return &gql_model.AdminOperationResult{
		Success:       true,
		Message:       "Activity tier created successfully",
		AffectedCount: intPtr(1),
	}, nil
}

// AdminUpdateActivityTier updates an existing activity tier
func (r *AdminActivityResolver) AdminUpdateActivityTier(ctx context.Context, input gql_model.UpdateActivityTierInput) (*gql_model.AdminOperationResult, error) {
	if !IsAdminUser(ctx) {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: "Admin access required",
		}, nil
	}

	// Get existing tier
	tierLevel := 0
	if input.Level != nil {
		tierLevel = *input.Level
	}
	existingTier, err := r.tierService.GetTierByLevel(ctx, tierLevel)
	if err != nil {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: fmt.Sprintf("Failed to get existing tier: %v", err),
		}, nil
	}

	// Update fields if provided
	if input.Name != nil {
		existingTier.Name = *input.Name
	}
	if input.Level != nil {
		existingTier.Level = *input.Level
	}
	if input.MinPoints != nil {
		existingTier.MinPoints = *input.MinPoints
	}
	if input.MaxPoints != nil {
		existingTier.MaxPoints = input.MaxPoints
	}
	if input.Color != nil {
		existingTier.Color = *input.Color
	}
	if input.Icon != nil {
		existingTier.Icon = *input.Icon
	}
	if input.Description != nil {
		existingTier.Description = *input.Description
	}
	if input.CashbackPercentage != nil {
		existingTier.CashbackPercentage = decimal.NewFromFloat(*input.CashbackPercentage)
	}
	if input.Benefits != nil {
		existingTier.Benefits = *input.Benefits
	}

	if err := r.adminService.UpdateActivityTier(ctx, existingTier); err != nil {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: fmt.Sprintf("Failed to update tier: %v", err),
		}, nil
	}

	return &gql_model.AdminOperationResult{
		Success:       true,
		Message:       "Activity tier updated successfully",
		AffectedCount: intPtr(1),
	}, nil
}

// Helper functions
func IsAdminUser(ctx context.Context) bool {
	userID, ok := ctx.Value("user_id").(uuid.UUID)
	if !ok {
		return false
	}

	// Use the global admin auth service
	return middleware.GlobalAdminAuthService.IsAdminUser(userID)
}

// AdminDeleteActivityTier deletes an activity tier
func (r *AdminActivityResolver) AdminDeleteActivityTier(ctx context.Context, id int) (*gql_model.AdminOperationResult, error) {
	if !IsAdminUser(ctx) {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: "Admin access required",
		}, nil
	}

	if err := r.adminService.DeleteActivityTier(ctx, uint(id)); err != nil {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: fmt.Sprintf("Failed to delete tier: %v", err),
		}, nil
	}

	return &gql_model.AdminOperationResult{
		Success:       true,
		Message:       "Activity tier deleted successfully",
		AffectedCount: intPtr(1),
	}, nil
}

// AdminCreateActivityTask creates a new activity task
func (r *AdminActivityResolver) AdminCreateActivityTask(ctx context.Context, input gql_model.CreateActivityTaskInput) (*gql_model.AdminOperationResult, error) {
	if !IsAdminUser(ctx) {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: "Admin access required",
		}, nil
	}

	task := &model.ActivityTask{
		Name:            input.Name,
		Description:     stringValue(input.Description),
		Type:            convertTaskTypeFromGQL(input.Type),
		Category:        convertTaskCategoryFromGQL(input.Category),
		Status:          convertTaskStatusFromGQL(input.Status),
		RequiredCount:   input.RequiredCount,
		RequiredVolume:  decimal.NewFromFloat(input.RequiredVolume),
		MinVolume:       decimal.NewFromFloat(input.MinVolume),
		TimeWindowHours: input.TimeWindowHours,
		ValidFrom:       input.ValidFrom,
		ValidUntil:      input.ValidUntil,
		PointReward:     input.PointReward,
		CashbackReward:  decimal.NewFromFloat(input.CashbackReward),
		Config:          stringValue(input.Config),
		Metadata:        stringValue(input.Metadata),
		Icon:            stringValue(input.Icon),
		Color:           stringValue(input.Color),
		Priority:        input.Priority,
		IsHighlight:     input.IsHighlight,
	}

	if err := r.adminService.CreateActivityTask(ctx, task); err != nil {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: fmt.Sprintf("Failed to create task: %v", err),
		}, nil
	}

	return &gql_model.AdminOperationResult{
		Success:       true,
		Message:       "Activity task created successfully",
		AffectedCount: intPtr(1),
	}, nil
}

// AdminAdjustUserPoints adjusts user points manually
func (r *AdminActivityResolver) AdminAdjustUserPoints(ctx context.Context, input gql_model.ManualPointAdjustmentInput) (*gql_model.AdminOperationResult, error) {
	if !IsAdminUser(ctx) {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: "Admin access required",
		}, nil
	}

	userID, err := uuid.Parse(input.UserID)
	if err != nil {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: "Invalid user ID",
		}, nil
	}

	description := ""
	if input.Description != nil {
		description = *input.Description
	}

	if err := r.adminService.AdjustUserPoints(ctx, userID, input.Points, input.Reason, description); err != nil {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: fmt.Sprintf("Failed to adjust user points: %v", err),
		}, nil
	}

	return &gql_model.AdminOperationResult{
		Success: true,
		Message: "User points adjusted successfully",
	}, nil
}

// AdminResetDailyTasks resets all daily tasks
func (r *AdminActivityResolver) AdminResetDailyTasks(ctx context.Context) (*gql_model.AdminOperationResult, error) {
	if !IsAdminUser(ctx) {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: "Admin access required",
		}, nil
	}

	if err := r.adminService.ResetAllDailyTasks(ctx); err != nil {
		return &gql_model.AdminOperationResult{
			Success: false,
			Message: fmt.Sprintf("Failed to reset daily tasks: %v", err),
		}, nil
	}

	return &gql_model.AdminOperationResult{
		Success: true,
		Message: "Daily tasks reset successfully",
	}, nil
}

// Helper conversion functions
func convertTaskTypeFromGQL(gqlType gql_model.TaskType) model.TaskType {
	switch gqlType {
	case gql_model.TaskTypeDaily:
		return model.TaskTypeDaily
	case gql_model.TaskTypeCommunity:
		return model.TaskTypeCommunity
	case gql_model.TaskTypeTrading:
		return model.TaskTypeTrading
	case gql_model.TaskTypeOneTime:
		return model.TaskTypeOneTime
	case gql_model.TaskTypeUnlimited:
		return model.TaskTypeUnlimited
	case gql_model.TaskTypeProgress:
		return model.TaskTypeProgress
	case gql_model.TaskTypeManualUpdate:
		return model.TaskTypeManualUpdate
	default:
		return model.TaskTypeDaily
	}
}

func convertTaskCategoryFromGQL(gqlCategory gql_model.TaskCategory) model.TaskCategory {
	switch gqlCategory {
	case gql_model.TaskCategoryDailyTasks:
		return model.TaskCategoryDaily
	case gql_model.TaskCategoryCommunityTasks:
		return model.TaskCategoryCommunity
	case gql_model.TaskCategoryTradingTasks:
		return model.TaskCategoryTrading
	default:
		return model.TaskCategoryDaily
	}
}

func convertTaskStatusFromGQL(gqlStatus gql_model.TaskStatus) model.TaskStatus {
	switch gqlStatus {
	case gql_model.TaskStatusActive:
		return model.TaskStatusActive
	case gql_model.TaskStatusInactive:
		return model.TaskStatusInactive
	case gql_model.TaskStatusArchived:
		return model.TaskStatusArchived
	default:
		return model.TaskStatusActive
	}
}

func stringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}
