package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.76

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

// CreateUserWithReferral is the resolver for the createUserWithReferral field.
func (r *mutationResolver) CreateUserWithReferral(ctx context.Context, input gql_model.CreateUserWithReferralInput) (*gql_model.CreateUserResponse, error) {
	invitationResolver := resolvers.NewInvitationResolver()
	return invitationResolver.CreateUserWithReferral(ctx, input)
}

// CreateUserInvitationCode is the resolver for the createUserInvitationCode field.
func (r *mutationResolver) CreateUserInvitationCode(ctx context.Context, input gql_model.CreateUserInvitationCodeInput) (*gql_model.CreateUserResponse, error) {
	invitationResolver := resolvers.NewInvitationResolver()
	return invitationResolver.CreateUserInvitationCode(ctx, input)
}

// UpdateLevelCommission is the resolver for the updateLevelCommission field.
func (r *mutationResolver) UpdateLevelCommission(ctx context.Context, input gql_model.UpdateLevelCommissionInput) (*gql_model.UpdateLevelCommissionResponse, error) {
	levelResolver := resolvers.NewLevelResolver()
	return levelResolver.UpdateLevelCommission(ctx, input)
}

// ClaimActivityCashback is the resolver for the claimActivityCashback field.
func (r *mutationResolver) ClaimActivityCashback(ctx context.Context, input gql_model.ClaimActivityCashbackInput) (*gql_model.ClaimResultResponse, error) {
	claimResolver := resolvers.NewClaimResolver()
	return claimResolver.ClaimActivityCashback(ctx, input)
}

// ClaimAgentReferral is the resolver for the claimAgentReferral field.
func (r *mutationResolver) ClaimAgentReferral(ctx context.Context, input gql_model.ClaimAgentReferralInput) (*gql_model.ClaimResultResponse, error) {
	claimResolver := resolvers.NewClaimResolver()
	return claimResolver.ClaimAgentReferral(ctx, input)
}

// ClaimTaskReward is the resolver for the claimTaskReward field.
func (r *mutationResolver) ClaimTaskReward(ctx context.Context, input gql_model.ClaimTaskRewardInput) (*gql_model.TaskRewardClaimResponse, error) {
	panic(fmt.Errorf("not implemented: ClaimTaskReward - claimTaskReward"))
}

// RefreshUserTasks is the resolver for the refreshUserTasks field.
func (r *mutationResolver) RefreshUserTasks(ctx context.Context, input *gql_model.RefreshUserTasksInput) (*gql_model.UserTasksResponse, error) {
	panic(fmt.Errorf("not implemented: RefreshUserTasks - refreshUserTasks"))
}

// CompleteTask is the resolver for the completeTask field.
func (r *mutationResolver) CompleteTask(ctx context.Context, input gql_model.CompleteTaskInput) (*gql_model.TaskRewardClaimResponse, error) {
	panic(fmt.Errorf("not implemented: CompleteTask - completeTask"))
}

// ReferralSnapshot is the resolver for the referralSnapshot field.
func (r *queryResolver) ReferralSnapshot(ctx context.Context) (*gql_model.ReferralSnapshot, error) {
	invitationResolver := resolvers.NewInvitationResolver()
	return invitationResolver.ReferralSnapshot(ctx)
}

// AgentLevels is the resolver for the agentLevels field.
func (r *queryResolver) AgentLevels(ctx context.Context) ([]*gql_model.AgentLevel, error) {
	levelResolver := resolvers.NewLevelResolver()
	return levelResolver.AgentLevels(ctx)
}

// AgentLevel is the resolver for the agentLevel field.
func (r *queryResolver) AgentLevel(ctx context.Context, id int) (*gql_model.AgentLevel, error) {
	levelResolver := resolvers.NewLevelResolver()
	return levelResolver.AgentLevel(ctx, id)
}

// TransactionData is the resolver for the transactionData field.
func (r *queryResolver) TransactionData(ctx context.Context, input gql_model.TransactionDataInput) (*gql_model.TransactionDataResponse, error) {
	transactionResolver := resolvers.NewTransactionResolver()
	return transactionResolver.TransactionData(ctx, input)
}

// DataOverview is the resolver for the dataOverview field.
func (r *queryResolver) DataOverview(ctx context.Context, input gql_model.DataOverviewInput) (*gql_model.DataOverviewWithSummary, error) {
	return r.DataOverviewService.DataOverview(ctx, input)
}

// UserInvitationData is the resolver for the userInvitationData field.
func (r *queryResolver) UserInvitationData(ctx context.Context) (*gql_model.UserInvitationDataResponse, error) {
	invitationResolver := resolvers.NewInvitationResolver()
	return invitationResolver.UserInvitationData(ctx)
}

// RewardData is the resolver for the rewardData field.
func (r *queryResolver) RewardData(ctx context.Context) (*gql_model.RewardDataResponse, error) {
	return r.RewardService.RewardData(ctx)
}

// InvitationRecords is the resolver for the invitationRecords field.
func (r *queryResolver) InvitationRecords(ctx context.Context) (*gql_model.InvitationRecordResponse, error) {
	return r.RewardService.InvitationRecords(ctx)
}

// GetClaimReward is the resolver for the getClaimReward field.
func (r *queryResolver) GetClaimReward(ctx context.Context) (*gql_model.ClaimRewardResponse, error) {
	claimResolver := resolvers.NewClaimResolver()
	return claimResolver.GetClaimReward(ctx)
}

// ActivityTiers is the resolver for the activityTiers field.
func (r *queryResolver) ActivityTiers(ctx context.Context) (*gql_model.ActivityTierResponse, error) {
	panic(fmt.Errorf("not implemented: ActivityTiers - activityTiers"))
}

// UserActivityLevel is the resolver for the userActivityLevel field.
func (r *queryResolver) UserActivityLevel(ctx context.Context) (*gql_model.UserActivityLevelResponse, error) {
	panic(fmt.Errorf("not implemented: UserActivityLevel - userActivityLevel"))
}

// UserTasks is the resolver for the userTasks field.
func (r *queryResolver) UserTasks(ctx context.Context, input *gql_model.GetUserTasksInput) (*gql_model.UserTasksResponse, error) {
	panic(fmt.Errorf("not implemented: UserTasks - userTasks"))
}

// UserActivityStats is the resolver for the userActivityStats field.
func (r *queryResolver) UserActivityStats(ctx context.Context) (*gql_model.UserActivityStatsResponse, error) {
	panic(fmt.Errorf("not implemented: UserActivityStats - userActivityStats"))
}

// UserActivityRewards is the resolver for the userActivityRewards field.
func (r *queryResolver) UserActivityRewards(ctx context.Context) (*gql_model.ActivityRewardsResponse, error) {
	panic(fmt.Errorf("not implemented: UserActivityRewards - userActivityRewards"))
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
