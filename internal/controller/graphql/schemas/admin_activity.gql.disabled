# Admin Activity System GraphQL Schema

# =====================================================
# Admin Input Types
# =====================================================

input CreateActivityTierInput {
  name: String!
  level: Int!
  minPoints: Int!
  maxPoints: Int
  color: String!
  icon: String
  description: String
  cashbackPercentage: Float!
  benefits: String
}

input UpdateActivityTierInput {
  id: Int!
  name: String
  level: Int
  minPoints: Int
  maxPoints: Int
  color: String
  icon: String
  description: String
  cashbackPercentage: Float
  benefits: String
}

input CreateActivityTaskInput {
  name: String!
  description: String
  type: TaskType!
  category: TaskCategory!
  status: TaskStatus!
  requiredCount: Int!
  requiredVolume: Float!
  minVolume: Float!
  timeWindowHours: Int
  validFrom: Time
  validUntil: Time
  pointReward: Int!
  cashbackReward: Float!
  config: String
  metadata: String
  icon: String
  color: String
  priority: Int!
  isHighlight: Boolean!
}

input UpdateActivityTaskInput {
  id: ID!
  name: String
  description: String
  type: TaskType
  category: TaskCategory
  status: TaskStatus
  requiredCount: Int
  requiredVolume: Float
  minVolume: Float
  timeWindowHours: Int
  validFrom: Time
  validUntil: Time
  pointReward: Int
  cashbackReward: Float
  config: String
  metadata: String
  icon: String
  color: String
  priority: Int
  isHighlight: Boolean
}

input AdminUserFilterInput {
  userIds: [ID!]
  tierIds: [Int!]
  minPoints: Int
  maxPoints: Int
  dateFrom: Time
  dateTo: Time
  limit: Int = 50
  offset: Int = 0
}

input AdminTaskFilterInput {
  types: [TaskType!]
  categories: [TaskCategory!]
  statuses: [TaskStatus!]
  dateFrom: Time
  dateTo: Time
  limit: Int = 50
  offset: Int = 0
}

input ManualPointAdjustmentInput {
  userId: ID!
  points: Int!
  reason: String!
  description: String
}

input BulkTaskOperationInput {
  taskIds: [ID!]!
  operation: BulkTaskOperation!
  newStatus: TaskStatus
}

enum BulkTaskOperation {
  ACTIVATE
  DEACTIVATE
  ARCHIVE
  DELETE
}

# =====================================================
# Admin Response Types
# =====================================================

type AdminActivityDashboard {
  totalUsers: Int!
  totalActiveTasks: Int!
  totalPointsAwarded: Int!
  totalCashbackDistributed: Float!
  tierDistribution: [TierDistribution!]!
  taskCompletionStats: [TaskCompletionStat!]!
  recentActivities: [RecentActivity!]!
  systemHealth: SystemHealth!
}

type TierDistribution {
  tier: ActivityTier!
  userCount: Int!
  percentage: Float!
}

type TaskCompletionStat {
  task: ActivityTask!
  completionCount: Int!
  completionRate: Float!
  averageTimeToComplete: Float
}

type RecentActivity {
  id: ID!
  userId: ID!
  userName: String
  activityType: String!
  description: String!
  pointsAwarded: Int
  timestamp: Time!
}

type SystemHealth {
  status: String!
  lastProcessedAt: Time
  pendingTasks: Int!
  errorCount: Int!
  uptime: Float!
}

type AdminUserActivityDetail {
  user: User!
  activityLevel: UserActivityLevel!
  recentTasks: [UserTaskProgress!]!
  pointHistory: [UserPointTransaction!]!
  tierHistory: [ActivityTierUpgrade!]!
  dailyActivities: [UserDailyActivity!]!
  stats: UserActivityStats!
}

type AdminTaskAnalytics {
  task: ActivityTask!
  totalCompletions: Int!
  uniqueUsers: Int!
  averageCompletionTime: Float
  completionsByDay: [DailyCompletionStat!]!
  topUsers: [TaskTopUser!]!
}

type DailyCompletionStat {
  date: String!
  completions: Int!
  uniqueUsers: Int!
}

type TaskTopUser {
  user: User!
  completionCount: Int!
  totalPointsEarned: Int!
  lastCompletedAt: Time!
}

type AdminOperationResult {
  success: Boolean!
  message: String!
  affectedCount: Int
  errors: [String!]
}

type AdminTaskListResponse {
  tasks: [ActivityTask!]!
  total: Int!
  hasMore: Boolean!
  success: Boolean!
  message: String
}

type AdminUserListResponse {
  users: [AdminUserActivityDetail!]!
  total: Int!
  hasMore: Boolean!
  success: Boolean!
  message: String
}

# =====================================================
# Admin Queries
# =====================================================

extend type Query {
  # Dashboard and Monitoring
  adminActivityDashboard: AdminActivityDashboard! @auth

  # Tier Management
  adminActivityTiers: [ActivityTier!]! @auth
  adminActivityTier(id: Int!): ActivityTier @auth

  # Task Management
  adminActivityTasks(filter: AdminTaskFilterInput): AdminTaskListResponse! @auth
  adminActivityTask(id: ID!): ActivityTask @auth
  adminTaskAnalytics(taskId: ID!): AdminTaskAnalytics! @auth

  # User Management
  adminUserActivities(filter: AdminUserFilterInput): AdminUserListResponse! @auth
  adminUserActivityDetail(userId: ID!): AdminUserActivityDetail! @auth

  # System Analytics
  adminActivityStats(dateFrom: Time!, dateTo: Time!): AdminActivityDashboard! @auth
  adminSystemHealth: SystemHealth! @auth

  # Export Data
  adminExportUserActivities(filter: AdminUserFilterInput): String! @auth
  adminExportTaskCompletions(filter: AdminTaskFilterInput): String! @auth
}

# =====================================================
# Admin Mutations
# =====================================================

extend type Mutation {
  # Tier Management
  adminCreateActivityTier(input: CreateActivityTierInput!): AdminOperationResult! @auth
  adminUpdateActivityTier(input: UpdateActivityTierInput!): AdminOperationResult! @auth
  adminDeleteActivityTier(id: Int!): AdminOperationResult! @auth

  # Task Management
  adminCreateActivityTask(input: CreateActivityTaskInput!): AdminOperationResult! @auth
  adminUpdateActivityTask(input: UpdateActivityTaskInput!): AdminOperationResult! @auth
  adminDeleteActivityTask(id: ID!): AdminOperationResult! @auth
  adminBulkTaskOperation(input: BulkTaskOperationInput!): AdminOperationResult! @auth

  # User Management
  adminAdjustUserPoints(input: ManualPointAdjustmentInput!): AdminOperationResult! @auth
  adminResetUserProgress(userId: ID!, taskId: ID!): AdminOperationResult! @auth
  adminForceUserTierUpgrade(userId: ID!, tierLevel: Int!): AdminOperationResult! @auth

  # System Operations
  adminResetDailyTasks: AdminOperationResult! @auth
  adminProcessExpiredTasks: AdminOperationResult! @auth
  adminRecalculateUserTiers: AdminOperationResult! @auth
  adminInitializeUserActivity(userId: ID!): AdminOperationResult! @auth

  # Bulk Operations
  adminBulkInitializeUsers(userIds: [ID!]!): AdminOperationResult! @auth
  adminBulkResetUserProgress(userIds: [ID!]!, taskIds: [ID!]): AdminOperationResult! @auth
}
