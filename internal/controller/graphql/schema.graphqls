scalar Time

directive @auth on FIELD_DEFINITION
directive @adminAuth on FIELD_DEFINITION

type Query {
  # Get referral snapshot
  referralSnapshot: ReferralSnapshot @auth

  # Get all agent levels
  agentLevels: [AgentLevel!]! @auth

  # Get specific agent level by ID
  agentLevel(id: Int!): AgentLevel @auth

  # Get transaction data (all, meme, contract)
  transactionData(input: TransactionDataInput!): TransactionDataResponse! @auth

  # Get data overview with charts
  dataOverview(input: DataOverviewInput!): DataOverviewWithSummary! @auth

  # Get user invitation data statistics
  userInvitationData: UserInvitationDataResponse! @auth

  # Get user reward data
  rewardData: RewardDataResponse! @auth

  # Get user invitation record
  invitationRecords: InvitationRecordResponse! @auth

  # Get user claim reward
  getClaimReward: ClaimRewardResponse! @auth

  # Activity System Queries
  # Get all activity tiers
  activityTiers: ActivityTierResponse! @auth

  # Get user's activity level and tier information
  userActivityLevel: UserActivityLevelResponse! @auth

  # Get user's tasks with optional filtering
  userTasks(input: GetUserTasksInput): UserTasksResponse! @auth

  # Get user's activity statistics
  userActivityStats: UserActivityStatsResponse! @auth

  # Get user's activity rewards
  userActivityRewards: ActivityRewardsResponse! @auth


}

type Mutation {
  # Create user with referral
  createUserWithReferral(input: CreateUserWithReferralInput!): CreateUserResponse! @auth

  # Create user invitation code
  createUserInvitationCode(input: CreateUserInvitationCodeInput!): CreateUserResponse! @auth

  # Update commission rates and meme fee rebate for a specific level
  updateLevelCommission(input: UpdateLevelCommissionInput!): UpdateLevelCommissionResponse! @auth

  # Claim activity cashback rewards
  claimActivityCashback(input: ClaimActivityCashbackInput!): ClaimResultResponse! @auth

  # Claim agent referral rewards
  claimAgentReferral(input: ClaimAgentReferralInput!): ClaimResultResponse! @auth

  # Activity System Mutations
  # Claim reward for a completed task
  claimTaskReward(input: ClaimTaskRewardInput!): TaskRewardClaimResponse! @auth

  # Refresh user's task list
  refreshUserTasks(input: RefreshUserTasksInput): UserTasksResponse! @auth

  # Manually complete a task (for manual verification tasks)
  completeTask(input: CompleteTaskInput!): TaskRewardClaimResponse! @auth


}
