package initializer

import (
	"context"

	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity"
	activityService "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity"
)

// InitActivitySystem initializes the activity system
func InitActivitySystem() error {
	global.GVA_LOG.Info("Initializing Activity System")

	ctx := context.Background()

	// Initialize repositories
	tierRepo := activity.NewActivityTierRepository()
	taskRepo := activity.NewActivityTaskRepository()
	rewardRepo := activity.NewActivityRewardRepository()

	// Initialize services
	tierService := activityService.NewActivityTierService(tierRepo)
	taskService := activityService.NewActivityTaskService(taskRepo, tierService)
	integrationService := activityService.NewActivityIntegrationService(
		tierService,
		taskService,
		rewardRepo,
		taskRepo,
		tierRepo,
	)

	// Create default tiers
	if err := tierService.CreateDefaultTiers(ctx); err != nil {
		global.GVA_LOG.Error("Failed to create default activity tiers", zap.Error(err))
		return err
	}

	// Create default tasks
	if err := integrationService.CreateDefaultTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to create default activity tasks", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("Activity System initialized successfully")
	return nil
}

// GetActivityServices returns the activity services for use in other parts of the application
func GetActivityServices() (*activityService.ActivityTierService, *activityService.ActivityTaskService, *activityService.ActivityIntegrationService) {
	// Initialize repositories
	tierRepo := activity.NewActivityTierRepository()
	taskRepo := activity.NewActivityTaskRepository()
	rewardRepo := activity.NewActivityRewardRepository()

	// Initialize services
	tierService := activityService.NewActivityTierService(tierRepo)
	taskService := activityService.NewActivityTaskService(taskRepo, tierService)
	integrationService := activityService.NewActivityIntegrationService(
		tierService,
		taskService,
		rewardRepo,
		taskRepo,
		tierRepo,
	)

	return tierService.(*activityService.ActivityTierService), 
		   taskService.(*activityService.ActivityTaskService), 
		   integrationService
}
