package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// TaskType represents different types of tasks
type TaskType string

const (
	TaskTypeDaily        TaskType = "DAILY"         // Daily tasks that refresh at UTC 0:00
	TaskTypeCommunity    TaskType = "COMMUNITY"     // Community tasks (social media, etc.)
	TaskTypeTrading      TaskType = "TRADING"       // Trading volume/activity tasks
	TaskTypeOneTime      TaskType = "ONE_TIME"      // One-time tasks
	TaskTypeUnlimited    TaskType = "UNLIMITED"     // Unlimited tasks (can be completed multiple times)
	TaskTypeProgress     TaskType = "PROGRESS"      // Progress tasks (streaks, cumulative goals)
	TaskTypeManualUpdate TaskType = "MANUAL_UPDATE" // Manual update tasks (social media verification)
)

// TaskCategory represents task categories for UI organization
type TaskCategory string

const (
	TaskCategoryDaily     TaskCategory = "DAILY_TASKS"
	TaskCategoryCommunity TaskCategory = "COMMUNITY_TASKS"
	TaskCategoryTrading   TaskCategory = "TRADING_TASKS"
)

// TaskStatus represents the status of a task
type TaskStatus string

const (
	TaskStatusActive   TaskStatus = "ACTIVE"
	TaskStatusInactive TaskStatus = "INACTIVE"
	TaskStatusArchived TaskStatus = "ARCHIVED"
)

// UserTaskStatus represents user's progress status on a task
type UserTaskStatus string

const (
	UserTaskStatusNotStarted UserTaskStatus = "NOT_STARTED"
	UserTaskStatusInProgress UserTaskStatus = "IN_PROGRESS"
	UserTaskStatusCompleted  UserTaskStatus = "COMPLETED"
	UserTaskStatusClaimed    UserTaskStatus = "CLAIMED"
	UserTaskStatusExpired    UserTaskStatus = "EXPIRED"
)

// ActivityTask represents a task that users can complete to earn points
type ActivityTask struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Name        string    `gorm:"type:varchar(255);not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	
	// Task Classification
	Type     TaskType     `gorm:"type:varchar(20);not null" json:"type"`
	Category TaskCategory `gorm:"type:varchar(20);not null" json:"category"`
	Status   TaskStatus   `gorm:"type:varchar(20);not null;default:'ACTIVE'" json:"status"`

	// Task Requirements
	RequiredCount  int             `gorm:"not null;default:1" json:"required_count"`        // Number of times to complete
	RequiredVolume decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"required_volume"` // Required trading volume
	MinVolume      decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"min_volume"`      // Minimum volume per transaction
	
	// Time Constraints
	TimeWindowHours *int       `json:"time_window_hours"` // Time window for completion (null for no limit)
	ValidFrom       *time.Time `json:"valid_from"`        // Task becomes available from this time
	ValidUntil      *time.Time `json:"valid_until"`       // Task expires at this time
	
	// Rewards
	PointReward    int             `gorm:"not null;default:0" json:"point_reward"`    // Points awarded for completion
	CashbackReward decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"cashback_reward"` // Additional cashback reward
	
	// Task Configuration (JSON for flexibility)
	Config   string `gorm:"type:text" json:"config"`   // JSON configuration for task-specific settings
	Metadata string `gorm:"type:text" json:"metadata"` // Additional metadata
	
	// Display Properties
	Icon        string `gorm:"type:varchar(100)" json:"icon"`
	Color       string `gorm:"type:varchar(20)" json:"color"`
	Priority    int    `gorm:"not null;default:0" json:"priority"` // Display priority (higher = more important)
	IsHighlight bool   `gorm:"not null;default:false" json:"is_highlight"` // Highlight in UI
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	UserProgress []UserTaskProgress `gorm:"foreignKey:TaskID;references:ID" json:"user_progress"`
}

// UserTaskProgress represents a user's progress on a specific task
type UserTaskProgress struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	TaskID uuid.UUID `gorm:"type:uuid;not null;index" json:"task_id"`

	// Progress Tracking
	Status         UserTaskStatus  `gorm:"type:varchar(20);not null;default:'NOT_STARTED'" json:"status"`
	CurrentCount   int             `gorm:"not null;default:0" json:"current_count"`   // Current progress count
	CurrentVolume  decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"current_volume"` // Current volume progress
	CompletedCount int             `gorm:"not null;default:0" json:"completed_count"` // Number of times completed (for unlimited tasks)
	
	// Streak Tracking (for progress tasks)
	CurrentStreak int        `gorm:"not null;default:0" json:"current_streak"`
	BestStreak    int        `gorm:"not null;default:0" json:"best_streak"`
	LastStreakAt  *time.Time `json:"last_streak_at"`
	
	// Time Tracking
	StartedAt    *time.Time `json:"started_at"`
	CompletedAt  *time.Time `json:"completed_at"`
	ClaimedAt    *time.Time `json:"claimed_at"`
	ExpiresAt    *time.Time `json:"expires_at"`
	LastUpdateAt *time.Time `json:"last_update_at"`
	
	// Period Tracking (for daily/weekly tasks)
	PeriodStart *time.Time `json:"period_start"`
	PeriodEnd   *time.Time `json:"period_end"`
	
	// Rewards Tracking
	PointsEarned    int             `gorm:"not null;default:0" json:"points_earned"`
	CashbackEarned  decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"cashback_earned"`
	TotalRewardUSD  decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_reward_usd"`
	
	// Additional Data
	Metadata string `gorm:"type:text" json:"metadata"` // JSON for task-specific data
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	User User         `gorm:"foreignKey:UserID;references:ID" json:"user"`
	Task ActivityTask `gorm:"foreignKey:TaskID;references:ID" json:"task"`
}

// TaskCompletion represents a single completion of a task (for tracking history)
type TaskCompletion struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	TaskID uuid.UUID `gorm:"type:uuid;not null;index" json:"task_id"`
	
	// Completion Details
	CompletedAt    time.Time       `gorm:"not null" json:"completed_at"`
	PointsAwarded  int             `gorm:"not null;default:0" json:"points_awarded"`
	CashbackAmount decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"cashback_amount"`
	
	// Context Information
	TriggerType   string `gorm:"type:varchar(50);not null" json:"trigger_type"`   // "MANUAL", "AUTOMATIC", "SYSTEM"
	TriggerSource string `gorm:"type:varchar(100)" json:"trigger_source"`         // Source that triggered completion
	
	// Reference Data
	ReferenceID   *uuid.UUID `gorm:"type:uuid" json:"reference_id"`   // Reference to transaction, etc.
	ReferenceType string     `gorm:"type:varchar(50)" json:"reference_type"` // Type of reference
	
	// Additional Data
	CompletionData string `gorm:"type:text" json:"completion_data"` // JSON data about completion
	
	CreatedAt time.Time `json:"created_at"`

	// Relationships
	User User         `gorm:"foreignKey:UserID;references:ID" json:"user"`
	Task ActivityTask `gorm:"foreignKey:TaskID;references:ID" json:"task"`
}

// TableName methods for custom table names
func (ActivityTask) TableName() string {
	return "activity_tasks"
}

func (UserTaskProgress) TableName() string {
	return "user_task_progress"
}

func (TaskCompletion) TableName() string {
	return "task_completions"
}
