package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ActivityRewardType represents different types of activity rewards
type ActivityRewardType string

const (
	ActivityRewardTypePoints   ActivityRewardType = "POINTS"
	ActivityRewardTypeCashback ActivityRewardType = "CASHBACK"
	ActivityRewardTypeBonus    ActivityRewardType = "BONUS"
	ActivityRewardTypeTier     ActivityRewardType = "TIER_UPGRADE"
)

// ActivityRewardStatus represents the status of a reward
type ActivityRewardStatus string

const (
	ActivityRewardStatusPending ActivityRewardStatus = "PENDING"
	ActivityRewardStatusClaimed ActivityRewardStatus = "CLAIMED"
	ActivityRewardStatusExpired ActivityRewardStatus = "EXPIRED"
)

// ActivityReward represents rewards earned from activities
type ActivityReward struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`

	// Reward Details
	Type        ActivityRewardType   `gorm:"type:varchar(20);not null" json:"type"`
	Status      ActivityRewardStatus `gorm:"type:varchar(20);not null;default:'PENDING'" json:"status"`
	Amount      decimal.Decimal      `gorm:"type:numeric(38,18);not null;default:0" json:"amount"` // Points or cashback amount
	AmountUSD   decimal.Decimal      `gorm:"type:numeric(38,18);default:0" json:"amount_usd"`     // USD equivalent
	Currency    string               `gorm:"type:varchar(10);default:'POINTS'" json:"currency"`   // "POINTS", "USD", "SOL"
	
	// Source Information
	SourceType string     `gorm:"type:varchar(50);not null" json:"source_type"` // "TASK", "TRADING", "MANUAL", "TIER_BONUS"
	SourceID   *uuid.UUID `gorm:"type:uuid" json:"source_id"`                   // ID of source (task_id, transaction_id, etc.)
	SourceName string     `gorm:"type:varchar(255)" json:"source_name"`         // Human readable source name
	
	// Timing
	EarnedAt  time.Time  `gorm:"not null" json:"earned_at"`
	ClaimedAt *time.Time `json:"claimed_at"`
	ExpiresAt *time.Time `json:"expires_at"`
	
	// Additional Information
	Description string `gorm:"type:text" json:"description"`
	Metadata    string `gorm:"type:text" json:"metadata"` // JSON for additional data
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;references:ID" json:"user"`
}

// UserActivityStats represents aggregated user activity statistics
type UserActivityStats struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;unique;index" json:"user_id"`
	
	// Daily Stats (reset daily at UTC 0:00)
	DailyTasksCompleted   int             `gorm:"not null;default:0" json:"daily_tasks_completed"`
	DailyPointsEarned     int             `gorm:"not null;default:0" json:"daily_points_earned"`
	DailyCashbackEarned   decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"daily_cashback_earned"`
	DailyTradingVolume    decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"daily_trading_volume"`
	DailyLastReset        time.Time       `gorm:"not null;default:CURRENT_TIMESTAMP" json:"daily_last_reset"`
	
	// Weekly Stats (reset weekly on Monday UTC 0:00)
	WeeklyTasksCompleted  int             `gorm:"not null;default:0" json:"weekly_tasks_completed"`
	WeeklyPointsEarned    int             `gorm:"not null;default:0" json:"weekly_points_earned"`
	WeeklyCashbackEarned  decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"weekly_cashback_earned"`
	WeeklyTradingVolume   decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"weekly_trading_volume"`
	WeeklyLastReset       time.Time       `gorm:"not null;default:CURRENT_TIMESTAMP" json:"weekly_last_reset"`
	
	// Monthly Stats (reset monthly on 1st UTC 0:00)
	MonthlyTasksCompleted int             `gorm:"not null;default:0" json:"monthly_tasks_completed"`
	MonthlyPointsEarned   int             `gorm:"not null;default:0" json:"monthly_points_earned"`
	MonthlyCashbackEarned decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"monthly_cashback_earned"`
	MonthlyTradingVolume  decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"monthly_trading_volume"`
	MonthlyActiveDays     int             `gorm:"not null;default:0" json:"monthly_active_days"`
	MonthlyLastReset      time.Time       `gorm:"not null;default:CURRENT_TIMESTAMP" json:"monthly_last_reset"`
	
	// Lifetime Stats
	TotalTasksCompleted   int             `gorm:"not null;default:0" json:"total_tasks_completed"`
	TotalPointsEarned     int             `gorm:"not null;default:0" json:"total_points_earned"`
	TotalCashbackEarned   decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_cashback_earned"`
	TotalTradingVolume    decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_trading_volume"`
	TotalActiveDays       int             `gorm:"not null;default:0" json:"total_active_days"`
	
	// Streak Information
	CurrentLoginStreak    int        `gorm:"not null;default:0" json:"current_login_streak"`
	BestLoginStreak       int        `gorm:"not null;default:0" json:"best_login_streak"`
	CurrentTradingStreak  int        `gorm:"not null;default:0" json:"current_trading_streak"`
	BestTradingStreak     int        `gorm:"not null;default:0" json:"best_trading_streak"`
	LastActivityAt        *time.Time `json:"last_activity_at"`
	LastTradingAt         *time.Time `json:"last_trading_at"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;references:ID" json:"user"`
}

// ActivityCashbackClaim represents a claim for activity cashback rewards
type ActivityCashbackClaim struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`

	// Claim Details
	ClaimType     string          `gorm:"type:varchar(50);not null" json:"claim_type"` // "ACTIVITY_CASHBACK", "TIER_BONUS"
	TotalAmount   decimal.Decimal `gorm:"type:numeric(38,18);not null" json:"total_amount"`
	TotalAmountUSD decimal.Decimal `gorm:"type:numeric(38,18);not null" json:"total_amount_usd"`
	Currency      string          `gorm:"type:varchar(10);not null" json:"currency"` // "SOL", "USD"
	
	// Claim Status
	Status      string     `gorm:"type:varchar(20);not null;default:'PENDING'" json:"status"` // "PENDING", "PROCESSING", "COMPLETED", "FAILED"
	ClaimedAt   time.Time  `gorm:"not null" json:"claimed_at"`
	ProcessedAt *time.Time `json:"processed_at"`
	
	// Transaction Information
	TransactionHash *string `gorm:"type:varchar(255)" json:"transaction_hash"`
	BlockNumber     *uint64 `json:"block_number"`
	
	// Breakdown of claimed rewards
	RewardIDs []string `gorm:"type:text" json:"reward_ids"` // JSON array of reward IDs included in this claim
	
	// Additional Information
	Description string `gorm:"type:text" json:"description"`
	Metadata    string `gorm:"type:text" json:"metadata"` // JSON for additional data
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;references:ID" json:"user"`
}

// UserDailyActivity represents daily activity tracking for users
type UserDailyActivity struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	Date   time.Time `gorm:"type:date;not null;index" json:"date"` // Date in UTC
	
	// Activity Flags
	HasLogin         bool `gorm:"not null;default:false" json:"has_login"`
	HasTrading       bool `gorm:"not null;default:false" json:"has_trading"`
	HasTaskCompletion bool `gorm:"not null;default:false" json:"has_task_completion"`
	HasSocialActivity bool `gorm:"not null;default:false" json:"has_social_activity"`
	
	// Activity Counts
	TasksCompleted    int             `gorm:"not null;default:0" json:"tasks_completed"`
	PointsEarned      int             `gorm:"not null;default:0" json:"points_earned"`
	CashbackEarned    decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"cashback_earned"`
	TradingVolume     decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"trading_volume"`
	TransactionCount  int             `gorm:"not null;default:0" json:"transaction_count"`
	
	// First and Last Activity Times
	FirstActivityAt *time.Time `json:"first_activity_at"`
	LastActivityAt  *time.Time `json:"last_activity_at"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;references:ID" json:"user"`
}

// TableName methods for custom table names
func (ActivityReward) TableName() string {
	return "activity_rewards"
}

func (UserActivityStats) TableName() string {
	return "user_activity_stats"
}

func (ActivityCashbackClaim) TableName() string {
	return "activity_cashback_claims"
}

func (UserDailyActivity) TableName() string {
	return "user_daily_activities"
}
