package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ActivityTier represents the activity tier levels for cashback rewards
// This is separate from the referral commission tiers
type ActivityTier struct {
	ID          uint   `gorm:"primaryKey" json:"id"`                                    // 1, 2, 3, 4, 5
	Name        string `gorm:"type:varchar(50);not null" json:"name"`                  // "Bronze", "Silver", "Gold", "Platinum", "Diamond"
	Level       int    `gorm:"not null;unique" json:"level"`                           // 1, 2, 3, 4, 5
	MinPoints   int    `gorm:"not null" json:"min_points"`                             // Minimum points required for this tier
	MaxPoints   *int   `gorm:"" json:"max_points"`                                     // Maximum points for this tier (null for highest tier)
	Color       string `gorm:"type:varchar(20);not null" json:"color"`                 // Hex color for UI display
	Icon        string `gorm:"type:varchar(100)" json:"icon"`                          // Icon URL or identifier
	Description string `gorm:"type:text" json:"description"`                           // Tier description

	// Cashback Benefits
	CashbackPercentage decimal.Decimal `gorm:"type:numeric(10,6);not null;default:0" json:"cashback_percentage"` // e.g., 0.05 for 5%

	// Additional Benefits (stored as JSON for flexibility)
	Benefits string `gorm:"type:text" json:"benefits"` // JSON string of additional benefits

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// UserActivityLevel represents a user's current activity level and points
type UserActivityLevel struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;unique;index" json:"user_id"`

	// Current Tier Information
	ActivityTierID uint         `gorm:"not null;default:1" json:"activity_tier_id"` // Foreign key to ActivityTier
	ActivityTier   ActivityTier `gorm:"foreignKey:ActivityTierID" json:"activity_tier"`

	// Points System
	TotalPoints   int `gorm:"not null;default:0" json:"total_points"`   // Lifetime points earned
	CurrentPoints int `gorm:"not null;default:0" json:"current_points"` // Points for current tier calculation

	// Activity Metrics
	TotalTradingVolume   decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"total_trading_volume"`   // Combined MEME + futures volume
	MonthlyTradingVolume decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"monthly_trading_volume"` // Current month volume
	MonthlyActiveDays    int             `gorm:"not null;default:0" json:"monthly_active_days"`                        // Active days this month
	TotalCashbackEarned  decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"total_cashback_earned"`  // Total cashback earned

	// Tier History
	LastTierUpgrade   *time.Time `json:"last_tier_upgrade"`   // When user last upgraded tier
	TierUpgradeCount  int        `gorm:"not null;default:0" json:"tier_upgrade_count"`  // Number of times user upgraded
	PreviousTierID    *uint      `json:"previous_tier_id"`    // Previous tier ID for tracking
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	User         User                   `gorm:"foreignKey:UserID;references:ID" json:"user"`
	PointHistory []UserPointTransaction `gorm:"foreignKey:UserID;references:UserID" json:"point_history"`
}

// UserPointTransaction represents individual point transactions
type UserPointTransaction struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`

	// Transaction Details
	Points      int    `gorm:"not null" json:"points"`                                        // Points earned/spent (negative for spending)
	Type        string `gorm:"type:varchar(50);not null" json:"type"`                        // "TASK_COMPLETION", "DAILY_LOGIN", "TRADING_VOLUME", etc.
	Source      string `gorm:"type:varchar(100);not null" json:"source"`                     // Source identifier (task_id, activity_type, etc.)
	Description string `gorm:"type:text" json:"description"`                                 // Human readable description
	
	// Reference Information
	ReferenceID   *uuid.UUID `gorm:"type:uuid" json:"reference_id"`   // Reference to task, transaction, etc.
	ReferenceType string     `gorm:"type:varchar(50)" json:"reference_type"` // "TASK", "TRANSACTION", "MANUAL", etc.

	// Metadata
	Metadata string `gorm:"type:text" json:"metadata"` // JSON string for additional data

	CreatedAt time.Time `json:"created_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;references:ID" json:"user"`
}

// ActivityTierUpgrade represents tier upgrade history
type ActivityTierUpgrade struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`

	// Upgrade Details
	FromTierID uint `gorm:"not null" json:"from_tier_id"`
	ToTierID   uint `gorm:"not null" json:"to_tier_id"`
	
	// Points at time of upgrade
	PointsAtUpgrade int `gorm:"not null" json:"points_at_upgrade"`
	
	// Upgrade trigger
	TriggerType string `gorm:"type:varchar(50);not null" json:"trigger_type"` // "POINTS", "MANUAL", "SYSTEM"
	TriggerData string `gorm:"type:text" json:"trigger_data"`                 // JSON data about what triggered the upgrade

	CreatedAt time.Time `json:"created_at"`

	// Relationships
	User     User         `gorm:"foreignKey:UserID;references:ID" json:"user"`
	FromTier ActivityTier `gorm:"foreignKey:FromTierID;references:ID" json:"from_tier"`
	ToTier   ActivityTier `gorm:"foreignKey:ToTierID;references:ID" json:"to_tier"`
}

// TableName methods for custom table names
func (ActivityTier) TableName() string {
	return "activity_tiers"
}

func (UserActivityLevel) TableName() string {
	return "user_activity_levels"
}

func (UserPointTransaction) TableName() string {
	return "user_point_transactions"
}

func (ActivityTierUpgrade) TableName() string {
	return "activity_tier_upgrades"
}
