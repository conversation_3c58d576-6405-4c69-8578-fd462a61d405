package middleware

import (
	"context"
	"fmt"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
)

// AdminRole represents different admin roles
type AdminRole string

const (
	AdminRoleSuper     AdminRole = "SUPER_ADMIN"
	AdminRoleModerator AdminRole = "MODERATOR"
	AdminRoleAnalyst   AdminRole = "ANALYST"
	AdminRoleSupport   AdminRole = "SUPPORT"
)

// AdminPermission represents specific admin permissions
type AdminPermission string

const (
	// Activity System Permissions
	PermissionViewActivityDashboard AdminPermission = "VIEW_ACTIVITY_DASHBOARD"
	PermissionManageActivityTiers   AdminPermission = "MANAGE_ACTIVITY_TIERS"
	PermissionManageActivityTasks   AdminPermission = "MANAGE_ACTIVITY_TASKS"
	PermissionManageUserActivity    AdminPermission = "MANAGE_USER_ACTIVITY"
	PermissionViewActivityAnalytics AdminPermission = "VIEW_ACTIVITY_ANALYTICS"
	PermissionExportActivityData    AdminPermission = "EXPORT_ACTIVITY_DATA"
	PermissionSystemOperations      AdminPermission = "SYSTEM_OPERATIONS"

	// User Management Permissions
	PermissionViewUsers            AdminPermission = "VIEW_USERS"
	PermissionManageUsers          AdminPermission = "MANAGE_USERS"
	PermissionAdjustUserPoints     AdminPermission = "ADJUST_USER_POINTS"
	PermissionForceUserTierUpgrade AdminPermission = "FORCE_USER_TIER_UPGRADE"

	// System Permissions
	PermissionViewSystemHealth     AdminPermission = "VIEW_SYSTEM_HEALTH"
	PermissionManageSystemSettings AdminPermission = "MANAGE_SYSTEM_SETTINGS"
	PermissionViewLogs             AdminPermission = "VIEW_LOGS"
)

// AdminUser represents an admin user with roles and permissions
type AdminUser struct {
	ID          uuid.UUID         `json:"id"`
	UserID      uuid.UUID         `json:"user_id"`
	Email       string            `json:"email"`
	Role        AdminRole         `json:"role"`
	Permissions []AdminPermission `json:"permissions"`
	IsActive    bool              `json:"is_active"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// AdminAuthService handles admin authentication and authorization
type AdminAuthService struct {
	// In a real implementation, you might use a cache or database
	adminUsers map[uuid.UUID]*AdminUser
}

// NewAdminAuthService creates a new admin auth service
func NewAdminAuthService() *AdminAuthService {
	service := &AdminAuthService{
		adminUsers: make(map[uuid.UUID]*AdminUser),
	}

	// Initialize with default admin users (in production, this should come from database)
	service.initializeDefaultAdmins()

	return service
}

// initializeDefaultAdmins initializes default admin users
func (s *AdminAuthService) initializeDefaultAdmins() {
	// Super Admin with all permissions
	superAdminID := uuid.New()
	s.adminUsers[superAdminID] = &AdminUser{
		ID:     superAdminID,
		UserID: superAdminID,
		Email:  "<EMAIL>",
		Role:   AdminRoleSuper,
		Permissions: []AdminPermission{
			PermissionViewActivityDashboard,
			PermissionManageActivityTiers,
			PermissionManageActivityTasks,
			PermissionManageUserActivity,
			PermissionViewActivityAnalytics,
			PermissionExportActivityData,
			PermissionSystemOperations,
			PermissionViewUsers,
			PermissionManageUsers,
			PermissionAdjustUserPoints,
			PermissionForceUserTierUpgrade,
			PermissionViewSystemHealth,
			PermissionManageSystemSettings,
			PermissionViewLogs,
		},
		IsActive: true,
	}

	// Moderator with limited permissions
	moderatorID := uuid.New()
	s.adminUsers[moderatorID] = &AdminUser{
		ID:     moderatorID,
		UserID: moderatorID,
		Email:  "<EMAIL>",
		Role:   AdminRoleModerator,
		Permissions: []AdminPermission{
			PermissionViewActivityDashboard,
			PermissionManageActivityTasks,
			PermissionManageUserActivity,
			PermissionViewActivityAnalytics,
			PermissionViewUsers,
			PermissionAdjustUserPoints,
			PermissionViewSystemHealth,
		},
		IsActive: true,
	}

	// Analyst with read-only permissions
	analystID := uuid.New()
	s.adminUsers[analystID] = &AdminUser{
		ID:     analystID,
		UserID: analystID,
		Email:  "<EMAIL>",
		Role:   AdminRoleAnalyst,
		Permissions: []AdminPermission{
			PermissionViewActivityDashboard,
			PermissionViewActivityAnalytics,
			PermissionExportActivityData,
			PermissionViewUsers,
			PermissionViewSystemHealth,
			PermissionViewLogs,
		},
		IsActive: true,
	}
}

// GetAdminUser retrieves admin user by user ID
func (s *AdminAuthService) GetAdminUser(userID uuid.UUID) (*AdminUser, error) {
	adminUser, exists := s.adminUsers[userID]
	if !exists {
		return nil, fmt.Errorf("admin user not found")
	}

	if !adminUser.IsActive {
		return nil, fmt.Errorf("admin user is inactive")
	}

	return adminUser, nil
}

// HasPermission checks if admin user has specific permission
func (s *AdminAuthService) HasPermission(userID uuid.UUID, permission AdminPermission) bool {
	adminUser, err := s.GetAdminUser(userID)
	if err != nil {
		return false
	}

	// Super admin has all permissions
	if adminUser.Role == AdminRoleSuper {
		return true
	}

	// Check specific permissions
	for _, p := range adminUser.Permissions {
		if p == permission {
			return true
		}
	}

	return false
}

// IsAdminUser checks if user is an admin
func (s *AdminAuthService) IsAdminUser(userID uuid.UUID) bool {
	_, err := s.GetAdminUser(userID)
	return err == nil
}

// Global admin auth service instance
var GlobalAdminAuthService = NewAdminAuthService()

// AdminAuthDirective implements the @adminAuth GraphQL directive
func AdminAuthDirective(ctx context.Context, obj interface{}, next graphql.Resolver) (interface{}, error) {
	// Get user from context (assuming it's set by the main auth middleware)
	userID, ok := ctx.Value("user_id").(uuid.UUID)
	if !ok {
		global.GVA_LOG.Warn("Admin auth: No user ID in context")
		return nil, fmt.Errorf("authentication required")
	}

	// Check if user is admin
	if !GlobalAdminAuthService.IsAdminUser(userID) {
		global.GVA_LOG.Warn("Admin auth: User is not admin", zap.String("user_id", userID.String()))
		return nil, fmt.Errorf("admin access required")
	}

	global.GVA_LOG.Info("Admin auth: Access granted", zap.String("user_id", userID.String()))
	return next(ctx)
}

// AdminPermissionDirective implements permission-based authorization
func AdminPermissionDirective(permission AdminPermission) func(ctx context.Context, obj interface{}, next graphql.Resolver) (interface{}, error) {
	return func(ctx context.Context, obj interface{}, next graphql.Resolver) (interface{}, error) {
		// Get user from context
		userID, ok := ctx.Value("user_id").(uuid.UUID)
		if !ok {
			return nil, fmt.Errorf("authentication required")
		}

		// Check permission
		if !GlobalAdminAuthService.HasPermission(userID, permission) {
			global.GVA_LOG.Warn("Admin permission denied",
				zap.String("user_id", userID.String()),
				zap.String("permission", string(permission)))
			return nil, fmt.Errorf("insufficient permissions")
		}

		return next(ctx)
	}
}

// Helper functions for checking permissions in resolvers
func HasAdminPermission(ctx context.Context, permission AdminPermission) bool {
	userID, ok := ctx.Value("user_id").(uuid.UUID)
	if !ok {
		return false
	}

	return GlobalAdminAuthService.HasPermission(userID, permission)
}

func GetAdminUserFromContext(ctx context.Context) (*AdminUser, error) {
	userID, ok := ctx.Value("user_id").(uuid.UUID)
	if !ok {
		return nil, fmt.Errorf("no user in context")
	}

	return GlobalAdminAuthService.GetAdminUser(userID)
}

// AdminAuditLog represents an admin action audit log
type AdminAuditLog struct {
	ID          uuid.UUID `json:"id"`
	AdminUserID uuid.UUID `json:"admin_user_id"`
	Action      string    `json:"action"`
	Resource    string    `json:"resource"`
	ResourceID  *string   `json:"resource_id,omitempty"`
	Details     string    `json:"details"`
	IPAddress   string    `json:"ip_address"`
	UserAgent   string    `json:"user_agent"`
	Success     bool      `json:"success"`
	ErrorMsg    *string   `json:"error_msg,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
}

// LogAdminAction logs admin actions for audit purposes
func LogAdminAction(ctx context.Context, action, resource string, resourceID *string, details string, success bool, errorMsg *string) {
	userID, ok := ctx.Value("user_id").(uuid.UUID)
	if !ok {
		return
	}

	// In a real implementation, you would save this to database
	global.GVA_LOG.Info("Admin action logged",
		zap.String("admin_user_id", userID.String()),
		zap.String("action", action),
		zap.String("resource", resource),
		zap.String("details", details),
		zap.Bool("success", success))
}

// GetPermissionsForRole returns permissions for a specific role
func GetPermissionsForRole(role AdminRole) []AdminPermission {
	switch role {
	case AdminRoleSuper:
		return []AdminPermission{
			PermissionViewActivityDashboard,
			PermissionManageActivityTiers,
			PermissionManageActivityTasks,
			PermissionManageUserActivity,
			PermissionViewActivityAnalytics,
			PermissionExportActivityData,
			PermissionSystemOperations,
			PermissionViewUsers,
			PermissionManageUsers,
			PermissionAdjustUserPoints,
			PermissionForceUserTierUpgrade,
			PermissionViewSystemHealth,
			PermissionManageSystemSettings,
			PermissionViewLogs,
		}
	case AdminRoleModerator:
		return []AdminPermission{
			PermissionViewActivityDashboard,
			PermissionManageActivityTasks,
			PermissionManageUserActivity,
			PermissionViewActivityAnalytics,
			PermissionViewUsers,
			PermissionAdjustUserPoints,
			PermissionViewSystemHealth,
		}
	case AdminRoleAnalyst:
		return []AdminPermission{
			PermissionViewActivityDashboard,
			PermissionViewActivityAnalytics,
			PermissionExportActivityData,
			PermissionViewUsers,
			PermissionViewSystemHealth,
			PermissionViewLogs,
		}
	case AdminRoleSupport:
		return []AdminPermission{
			PermissionViewActivityDashboard,
			PermissionViewUsers,
			PermissionViewSystemHealth,
		}
	default:
		return []AdminPermission{}
	}
}
