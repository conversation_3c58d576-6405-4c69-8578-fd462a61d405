package activity

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// ActivityTaskRepositoryInterface defines the interface for activity task operations
type ActivityTaskRepositoryInterface interface {
	// Task Management
	GetAllTasks(ctx context.Context) ([]model.ActivityTask, error)
	GetTaskByID(ctx context.Context, id uuid.UUID) (*model.ActivityTask, error)
	GetTasksByType(ctx context.Context, taskType model.TaskType) ([]model.ActivityTask, error)
	GetTasksByCategory(ctx context.Context, category model.TaskCategory) ([]model.ActivityTask, error)
	GetActiveTasks(ctx context.Context) ([]model.ActivityTask, error)
	CreateTask(ctx context.Context, task *model.ActivityTask) error
	UpdateTask(ctx context.Context, task *model.ActivityTask) error
	DeleteTask(ctx context.Context, id uuid.UUID) error

	// User Task Progress
	GetUserTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error)
	GetUserTaskProgressByUser(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error)
	GetUserTaskProgressByStatus(ctx context.Context, userID uuid.UUID, status model.UserTaskStatus) ([]model.UserTaskProgress, error)
	CreateUserTaskProgress(ctx context.Context, progress *model.UserTaskProgress) error
	UpdateUserTaskProgress(ctx context.Context, progress *model.UserTaskProgress) error
	DeleteUserTaskProgress(ctx context.Context, userID, taskID uuid.UUID) error

	// Task Completion
	CreateTaskCompletion(ctx context.Context, completion *model.TaskCompletion) error
	GetUserTaskCompletions(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.TaskCompletion, error)
	GetTaskCompletionsByTask(ctx context.Context, taskID uuid.UUID) ([]model.TaskCompletion, error)
	GetUserTaskCompletionCount(ctx context.Context, userID, taskID uuid.UUID, since *time.Time) (int, error)

	// Daily Task Management
	GetDailyTasksForUser(ctx context.Context, userID uuid.UUID, date time.Time) ([]model.UserTaskProgress, error)
	ResetDailyTasks(ctx context.Context, userID uuid.UUID, date time.Time) error
	GetExpiredTasks(ctx context.Context, currentTime time.Time) ([]model.UserTaskProgress, error)
}

// ActivityTaskRepository implements the activity task repository interface
type ActivityTaskRepository struct {
	db *gorm.DB
}

// NewActivityTaskRepository creates a new activity task repository
func NewActivityTaskRepository() ActivityTaskRepositoryInterface {
	return &ActivityTaskRepository{
		db: global.GVA_DB,
	}
}

// GetAllTasks retrieves all tasks
func (r *ActivityTaskRepository) GetAllTasks(ctx context.Context) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).Order("priority DESC, created_at DESC").Find(&tasks).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get all tasks: %w", err)
	}
	return tasks, nil
}

// GetTaskByID retrieves a task by ID
func (r *ActivityTaskRepository) GetTaskByID(ctx context.Context, id uuid.UUID) (*model.ActivityTask, error) {
	var task model.ActivityTask
	err := r.db.WithContext(ctx).First(&task, "id = ?", id).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get task by ID: %w", err)
	}
	return &task, nil
}

// GetTasksByType retrieves tasks by type
func (r *ActivityTaskRepository) GetTasksByType(ctx context.Context, taskType model.TaskType) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Where("type = ? AND status = ?", taskType, model.TaskStatusActive).
		Order("priority DESC, created_at DESC").
		Find(&tasks).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get tasks by type: %w", err)
	}
	return tasks, nil
}

// GetTasksByCategory retrieves tasks by category
func (r *ActivityTaskRepository) GetTasksByCategory(ctx context.Context, category model.TaskCategory) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Where("category = ? AND status = ?", category, model.TaskStatusActive).
		Order("priority DESC, created_at DESC").
		Find(&tasks).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get tasks by category: %w", err)
	}
	return tasks, nil
}

// GetActiveTasks retrieves all active tasks
func (r *ActivityTaskRepository) GetActiveTasks(ctx context.Context) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	currentTime := time.Now()

	err := r.db.WithContext(ctx).
		Where("status = ?", model.TaskStatusActive).
		Where("(valid_from IS NULL OR valid_from <= ?)", currentTime).
		Where("(valid_until IS NULL OR valid_until >= ?)", currentTime).
		Order("priority DESC, created_at DESC").
		Find(&tasks).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get active tasks: %w", err)
	}
	return tasks, nil
}

// CreateTask creates a new task
func (r *ActivityTaskRepository) CreateTask(ctx context.Context, task *model.ActivityTask) error {
	err := r.db.WithContext(ctx).Create(task).Error
	if err != nil {
		return fmt.Errorf("failed to create task: %w", err)
	}
	return nil
}

// UpdateTask updates an existing task
func (r *ActivityTaskRepository) UpdateTask(ctx context.Context, task *model.ActivityTask) error {
	err := r.db.WithContext(ctx).Save(task).Error
	if err != nil {
		return fmt.Errorf("failed to update task: %w", err)
	}
	return nil
}

// DeleteTask deletes a task
func (r *ActivityTaskRepository) DeleteTask(ctx context.Context, id uuid.UUID) error {
	err := r.db.WithContext(ctx).Delete(&model.ActivityTask{}, "id = ?", id).Error
	if err != nil {
		return fmt.Errorf("failed to delete task: %w", err)
	}
	return nil
}

// GetUserTaskProgress retrieves user's progress on a specific task
func (r *ActivityTaskRepository) GetUserTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	var progress model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("User").
		Where("user_id = ? AND task_id = ?", userID, taskID).
		First(&progress).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user task progress: %w", err)
	}
	return &progress, nil
}

// GetUserTaskProgressByUser retrieves all task progress for a user
func (r *ActivityTaskRepository) GetUserTaskProgressByUser(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("Task").
		Where("user_id = ?", userID).
		Order("updated_at DESC").
		Find(&progress).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user task progress: %w", err)
	}
	return progress, nil
}

// GetUserTaskProgressByStatus retrieves user's task progress by status
func (r *ActivityTaskRepository) GetUserTaskProgressByStatus(ctx context.Context, userID uuid.UUID, status model.UserTaskStatus) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("Task").
		Where("user_id = ? AND status = ?", userID, status).
		Order("updated_at DESC").
		Find(&progress).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user task progress by status: %w", err)
	}
	return progress, nil
}

// CreateUserTaskProgress creates new user task progress
func (r *ActivityTaskRepository) CreateUserTaskProgress(ctx context.Context, progress *model.UserTaskProgress) error {
	err := r.db.WithContext(ctx).Create(progress).Error
	if err != nil {
		return fmt.Errorf("failed to create user task progress: %w", err)
	}
	return nil
}

// UpdateUserTaskProgress updates user task progress
func (r *ActivityTaskRepository) UpdateUserTaskProgress(ctx context.Context, progress *model.UserTaskProgress) error {
	err := r.db.WithContext(ctx).Save(progress).Error
	if err != nil {
		return fmt.Errorf("failed to update user task progress: %w", err)
	}
	return nil
}

// DeleteUserTaskProgress deletes user task progress
func (r *ActivityTaskRepository) DeleteUserTaskProgress(ctx context.Context, userID, taskID uuid.UUID) error {
	err := r.db.WithContext(ctx).
		Delete(&model.UserTaskProgress{}, "user_id = ? AND task_id = ?", userID, taskID).Error
	if err != nil {
		return fmt.Errorf("failed to delete user task progress: %w", err)
	}
	return nil
}

// CreateTaskCompletion creates a new task completion record
func (r *ActivityTaskRepository) CreateTaskCompletion(ctx context.Context, completion *model.TaskCompletion) error {
	err := r.db.WithContext(ctx).Create(completion).Error
	if err != nil {
		return fmt.Errorf("failed to create task completion: %w", err)
	}
	return nil
}

// GetUserTaskCompletions retrieves user's task completions with pagination
func (r *ActivityTaskRepository) GetUserTaskCompletions(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.TaskCompletion, error) {
	var completions []model.TaskCompletion
	err := r.db.WithContext(ctx).
		Preload("Task").
		Where("user_id = ?", userID).
		Order("completed_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user task completions: %w", err)
	}
	return completions, nil
}

// GetTaskCompletionsByTask retrieves all completions for a specific task
func (r *ActivityTaskRepository) GetTaskCompletionsByTask(ctx context.Context, taskID uuid.UUID) ([]model.TaskCompletion, error) {
	var completions []model.TaskCompletion
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("task_id = ?", taskID).
		Order("completed_at DESC").
		Find(&completions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get task completions by task: %w", err)
	}
	return completions, nil
}

// GetUserTaskCompletionCount counts user's completions for a task since a specific time
func (r *ActivityTaskRepository) GetUserTaskCompletionCount(ctx context.Context, userID, taskID uuid.UUID, since *time.Time) (int, error) {
	var count int64
	query := r.db.WithContext(ctx).
		Model(&model.TaskCompletion{}).
		Where("user_id = ? AND task_id = ?", userID, taskID)

	if since != nil {
		query = query.Where("completed_at >= ?", *since)
	}

	err := query.Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to get user task completion count: %w", err)
	}
	return int(count), nil
}

// GetDailyTasksForUser retrieves daily tasks for a user on a specific date
func (r *ActivityTaskRepository) GetDailyTasksForUser(ctx context.Context, userID uuid.UUID, date time.Time) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.UTC)
	endOfDay := startOfDay.Add(24 * time.Hour)

	err := r.db.WithContext(ctx).
		Preload("Task").
		Joins("JOIN activity_tasks ON user_task_progress.task_id = activity_tasks.id").
		Where("user_task_progress.user_id = ?", userID).
		Where("activity_tasks.type = ?", model.TaskTypeDaily).
		Where("user_task_progress.period_start >= ? AND user_task_progress.period_start < ?", startOfDay, endOfDay).
		Find(&progress).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get daily tasks for user: %w", err)
	}
	return progress, nil
}

// ResetDailyTasks resets daily tasks for a user
func (r *ActivityTaskRepository) ResetDailyTasks(ctx context.Context, userID uuid.UUID, date time.Time) error {
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.UTC)
	endOfDay := startOfDay.Add(24 * time.Hour)

	// Reset daily task progress
	err := r.db.WithContext(ctx).
		Model(&model.UserTaskProgress{}).
		Joins("JOIN activity_tasks ON user_task_progress.task_id = activity_tasks.id").
		Where("user_task_progress.user_id = ?", userID).
		Where("activity_tasks.type = ?", model.TaskTypeDaily).
		Where("user_task_progress.period_start >= ? AND user_task_progress.period_start < ?", startOfDay, endOfDay).
		Updates(map[string]interface{}{
			"status":         model.UserTaskStatusNotStarted,
			"current_count":  0,
			"current_volume": 0,
			"started_at":     nil,
			"completed_at":   nil,
			"claimed_at":     nil,
			"period_start":   startOfDay,
			"period_end":     endOfDay,
		}).Error
	if err != nil {
		return fmt.Errorf("failed to reset daily tasks: %w", err)
	}
	return nil
}

// GetExpiredTasks retrieves tasks that have expired
func (r *ActivityTaskRepository) GetExpiredTasks(ctx context.Context, currentTime time.Time) ([]model.UserTaskProgress, error) {
	var progress []model.UserTaskProgress
	err := r.db.WithContext(ctx).
		Preload("Task").
		Where("expires_at IS NOT NULL AND expires_at < ?", currentTime).
		Where("status NOT IN (?)", []model.UserTaskStatus{model.UserTaskStatusCompleted, model.UserTaskStatusClaimed, model.UserTaskStatusExpired}).
		Find(&progress).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get expired tasks: %w", err)
	}
	return progress, nil
}
