package activity

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// ActivityRewardRepositoryInterface defines the interface for activity reward operations
type ActivityRewardRepositoryInterface interface {
	// Activity Rewards
	CreateActivityReward(ctx context.Context, reward *model.ActivityReward) error
	GetActivityRewardByID(ctx context.Context, id uuid.UUID) (*model.ActivityReward, error)
	GetUserActivityRewards(ctx context.Context, userID uuid.UUID, status *model.ActivityRewardStatus, limit, offset int) ([]model.ActivityReward, error)
	UpdateActivityReward(ctx context.Context, reward *model.ActivityReward) error
	ClaimActivityReward(ctx context.Context, rewardID uuid.UUID) error
	GetPendingRewardsTotal(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)

	// User Activity Stats
	GetUserActivityStats(ctx context.Context, userID uuid.UUID) (*model.UserActivityStats, error)
	CreateUserActivityStats(ctx context.Context, stats *model.UserActivityStats) error
	UpdateUserActivityStats(ctx context.Context, stats *model.UserActivityStats) error
	ResetDailyStats(ctx context.Context, userID uuid.UUID) error
	ResetWeeklyStats(ctx context.Context, userID uuid.UUID) error
	ResetMonthlyStats(ctx context.Context, userID uuid.UUID) error

	// Activity Cashback Claims
	CreateActivityCashbackClaim(ctx context.Context, claim *model.ActivityCashbackClaim) error
	GetActivityCashbackClaimByID(ctx context.Context, id uuid.UUID) (*model.ActivityCashbackClaim, error)
	GetUserActivityCashbackClaims(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ActivityCashbackClaim, error)
	UpdateActivityCashbackClaim(ctx context.Context, claim *model.ActivityCashbackClaim) error

	// Daily Activity Tracking
	GetUserDailyActivity(ctx context.Context, userID uuid.UUID, date time.Time) (*model.UserDailyActivity, error)
	CreateUserDailyActivity(ctx context.Context, activity *model.UserDailyActivity) error
	UpdateUserDailyActivity(ctx context.Context, activity *model.UserDailyActivity) error
	GetUserActivityStreak(ctx context.Context, userID uuid.UUID, activityType string) (int, error)
}

// ActivityRewardRepository implements the activity reward repository interface
type ActivityRewardRepository struct {
	db *gorm.DB
}

// NewActivityRewardRepository creates a new activity reward repository
func NewActivityRewardRepository() ActivityRewardRepositoryInterface {
	return &ActivityRewardRepository{
		db: global.GVA_DB,
	}
}

// CreateActivityReward creates a new activity reward
func (r *ActivityRewardRepository) CreateActivityReward(ctx context.Context, reward *model.ActivityReward) error {
	err := r.db.WithContext(ctx).Create(reward).Error
	if err != nil {
		return fmt.Errorf("failed to create activity reward: %w", err)
	}
	return nil
}

// GetActivityRewardByID retrieves an activity reward by ID
func (r *ActivityRewardRepository) GetActivityRewardByID(ctx context.Context, id uuid.UUID) (*model.ActivityReward, error) {
	var reward model.ActivityReward
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&reward, "id = ?", id).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get activity reward by ID: %w", err)
	}
	return &reward, nil
}

// GetUserActivityRewards retrieves user's activity rewards with optional status filter
func (r *ActivityRewardRepository) GetUserActivityRewards(ctx context.Context, userID uuid.UUID, status *model.ActivityRewardStatus, limit, offset int) ([]model.ActivityReward, error) {
	var rewards []model.ActivityReward
	query := r.db.WithContext(ctx).Where("user_id = ?", userID)

	if status != nil {
		query = query.Where("status = ?", *status)
	}

	err := query.Order("earned_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&rewards).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user activity rewards: %w", err)
	}
	return rewards, nil
}

// UpdateActivityReward updates an activity reward
func (r *ActivityRewardRepository) UpdateActivityReward(ctx context.Context, reward *model.ActivityReward) error {
	err := r.db.WithContext(ctx).Save(reward).Error
	if err != nil {
		return fmt.Errorf("failed to update activity reward: %w", err)
	}
	return nil
}

// ClaimActivityReward marks an activity reward as claimed
func (r *ActivityRewardRepository) ClaimActivityReward(ctx context.Context, rewardID uuid.UUID) error {
	now := time.Now()
	err := r.db.WithContext(ctx).
		Model(&model.ActivityReward{}).
		Where("id = ? AND status = ?", rewardID, model.ActivityRewardStatusPending).
		Updates(map[string]interface{}{
			"status":     model.ActivityRewardStatusClaimed,
			"claimed_at": &now,
		}).Error
	if err != nil {
		return fmt.Errorf("failed to claim activity reward: %w", err)
	}
	return nil
}

// GetPendingRewardsTotal calculates total pending rewards for a user
func (r *ActivityRewardRepository) GetPendingRewardsTotal(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		Total decimal.Decimal
	}
	err := r.db.WithContext(ctx).
		Model(&model.ActivityReward{}).
		Select("COALESCE(SUM(amount_usd), 0) as total").
		Where("user_id = ? AND status = ?", userID, model.ActivityRewardStatusPending).
		Scan(&result).Error
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get pending rewards total: %w", err)
	}
	return result.Total, nil
}

// GetUserActivityStats retrieves user's activity statistics
func (r *ActivityRewardRepository) GetUserActivityStats(ctx context.Context, userID uuid.UUID) (*model.UserActivityStats, error) {
	var stats model.UserActivityStats
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		First(&stats).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user activity stats: %w", err)
	}
	return &stats, nil
}

// CreateUserActivityStats creates new user activity statistics
func (r *ActivityRewardRepository) CreateUserActivityStats(ctx context.Context, stats *model.UserActivityStats) error {
	err := r.db.WithContext(ctx).Create(stats).Error
	if err != nil {
		return fmt.Errorf("failed to create user activity stats: %w", err)
	}
	return nil
}

// UpdateUserActivityStats updates user activity statistics
func (r *ActivityRewardRepository) UpdateUserActivityStats(ctx context.Context, stats *model.UserActivityStats) error {
	err := r.db.WithContext(ctx).Save(stats).Error
	if err != nil {
		return fmt.Errorf("failed to update user activity stats: %w", err)
	}
	return nil
}

// ResetDailyStats resets daily statistics for a user
func (r *ActivityRewardRepository) ResetDailyStats(ctx context.Context, userID uuid.UUID) error {
	now := time.Now()
	err := r.db.WithContext(ctx).
		Model(&model.UserActivityStats{}).
		Where("user_id = ?", userID).
		Updates(map[string]interface{}{
			"daily_tasks_completed": 0,
			"daily_points_earned":   0,
			"daily_cashback_earned": decimal.Zero,
			"daily_trading_volume":  decimal.Zero,
			"daily_last_reset":      now,
		}).Error
	if err != nil {
		return fmt.Errorf("failed to reset daily stats: %w", err)
	}
	return nil
}

// ResetWeeklyStats resets weekly statistics for a user
func (r *ActivityRewardRepository) ResetWeeklyStats(ctx context.Context, userID uuid.UUID) error {
	now := time.Now()
	err := r.db.WithContext(ctx).
		Model(&model.UserActivityStats{}).
		Where("user_id = ?", userID).
		Updates(map[string]interface{}{
			"weekly_tasks_completed": 0,
			"weekly_points_earned":   0,
			"weekly_cashback_earned": decimal.Zero,
			"weekly_trading_volume":  decimal.Zero,
			"weekly_last_reset":      now,
		}).Error
	if err != nil {
		return fmt.Errorf("failed to reset weekly stats: %w", err)
	}
	return nil
}

// ResetMonthlyStats resets monthly statistics for a user
func (r *ActivityRewardRepository) ResetMonthlyStats(ctx context.Context, userID uuid.UUID) error {
	now := time.Now()
	err := r.db.WithContext(ctx).
		Model(&model.UserActivityStats{}).
		Where("user_id = ?", userID).
		Updates(map[string]interface{}{
			"monthly_tasks_completed": 0,
			"monthly_points_earned":   0,
			"monthly_cashback_earned": decimal.Zero,
			"monthly_trading_volume":  decimal.Zero,
			"monthly_active_days":     0,
			"monthly_last_reset":      now,
		}).Error
	if err != nil {
		return fmt.Errorf("failed to reset monthly stats: %w", err)
	}
	return nil
}

// CreateActivityCashbackClaim creates a new activity cashback claim
func (r *ActivityRewardRepository) CreateActivityCashbackClaim(ctx context.Context, claim *model.ActivityCashbackClaim) error {
	err := r.db.WithContext(ctx).Create(claim).Error
	if err != nil {
		return fmt.Errorf("failed to create activity cashback claim: %w", err)
	}
	return nil
}

// GetActivityCashbackClaimByID retrieves an activity cashback claim by ID
func (r *ActivityRewardRepository) GetActivityCashbackClaimByID(ctx context.Context, id uuid.UUID) (*model.ActivityCashbackClaim, error) {
	var claim model.ActivityCashbackClaim
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&claim, "id = ?", id).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get activity cashback claim by ID: %w", err)
	}
	return &claim, nil
}

// GetUserActivityCashbackClaims retrieves user's activity cashback claims
func (r *ActivityRewardRepository) GetUserActivityCashbackClaims(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ActivityCashbackClaim, error) {
	var claims []model.ActivityCashbackClaim
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("claimed_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&claims).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user activity cashback claims: %w", err)
	}
	return claims, nil
}

// UpdateActivityCashbackClaim updates an activity cashback claim
func (r *ActivityRewardRepository) UpdateActivityCashbackClaim(ctx context.Context, claim *model.ActivityCashbackClaim) error {
	err := r.db.WithContext(ctx).Save(claim).Error
	if err != nil {
		return fmt.Errorf("failed to update activity cashback claim: %w", err)
	}
	return nil
}

// GetUserDailyActivity retrieves user's daily activity for a specific date
func (r *ActivityRewardRepository) GetUserDailyActivity(ctx context.Context, userID uuid.UUID, date time.Time) (*model.UserDailyActivity, error) {
	var activity model.UserDailyActivity
	dateOnly := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.UTC)

	err := r.db.WithContext(ctx).
		Where("user_id = ? AND date = ?", userID, dateOnly).
		First(&activity).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user daily activity: %w", err)
	}
	return &activity, nil
}

// CreateUserDailyActivity creates a new user daily activity record
func (r *ActivityRewardRepository) CreateUserDailyActivity(ctx context.Context, activity *model.UserDailyActivity) error {
	err := r.db.WithContext(ctx).Create(activity).Error
	if err != nil {
		return fmt.Errorf("failed to create user daily activity: %w", err)
	}
	return nil
}

// UpdateUserDailyActivity updates user daily activity
func (r *ActivityRewardRepository) UpdateUserDailyActivity(ctx context.Context, activity *model.UserDailyActivity) error {
	err := r.db.WithContext(ctx).Save(activity).Error
	if err != nil {
		return fmt.Errorf("failed to update user daily activity: %w", err)
	}
	return nil
}

// GetUserActivityStreak calculates user's current activity streak
func (r *ActivityRewardRepository) GetUserActivityStreak(ctx context.Context, userID uuid.UUID, activityType string) (int, error) {
	var activities []model.UserDailyActivity

	// Get recent daily activities in descending order
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("date DESC").
		Limit(365). // Check up to 1 year
		Find(&activities).Error
	if err != nil {
		return 0, fmt.Errorf("failed to get user daily activities: %w", err)
	}

	streak := 0
	today := time.Now().UTC()
	currentDate := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, time.UTC)

	for _, activity := range activities {
		// Check if this date matches the expected consecutive date
		expectedDate := currentDate.AddDate(0, 0, -streak)
		if !activity.Date.Equal(expectedDate) {
			break
		}

		// Check if the specific activity type occurred on this date
		hasActivity := false
		switch activityType {
		case "login":
			hasActivity = activity.HasLogin
		case "trading":
			hasActivity = activity.HasTrading
		case "task":
			hasActivity = activity.HasTaskCompletion
		case "social":
			hasActivity = activity.HasSocialActivity
		default:
			hasActivity = activity.HasLogin || activity.HasTrading || activity.HasTaskCompletion || activity.HasSocialActivity
		}

		if !hasActivity {
			break
		}

		streak++
	}

	return streak, nil
}
