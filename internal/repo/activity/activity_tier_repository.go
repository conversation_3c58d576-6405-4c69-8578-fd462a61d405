package activity

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// ActivityTierRepositoryInterface defines the interface for activity tier operations
type ActivityTierRepositoryInterface interface {
	// Tier Management
	GetAllTiers(ctx context.Context) ([]model.ActivityTier, error)
	GetTierByID(ctx context.Context, id uint) (*model.ActivityTier, error)
	GetTierByLevel(ctx context.Context, level int) (*model.ActivityTier, error)
	CreateTier(ctx context.Context, tier *model.ActivityTier) error
	UpdateTier(ctx context.Context, tier *model.ActivityTier) error
	DeleteTier(ctx context.Context, id uint) error

	// User Activity Level Management
	GetUserActivityLevel(ctx context.Context, userID uuid.UUID) (*model.UserActivityLevel, error)
	CreateUserActivityLevel(ctx context.Context, level *model.UserActivityLevel) error
	UpdateUserActivityLevel(ctx context.Context, level *model.UserActivityLevel) error
	GetUsersByTier(ctx context.Context, tierID uint) ([]model.UserActivityLevel, error)

	// Point Transactions
	CreatePointTransaction(ctx context.Context, transaction *model.UserPointTransaction) error
	GetUserPointTransactions(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.UserPointTransaction, error)
	GetUserPointBalance(ctx context.Context, userID uuid.UUID) (int, error)
	GetUserPointTransactionsByType(ctx context.Context, userID uuid.UUID, transactionType string) ([]model.UserPointTransaction, error)

	// Tier Upgrades
	CreateTierUpgrade(ctx context.Context, upgrade *model.ActivityTierUpgrade) error
	GetUserTierUpgrades(ctx context.Context, userID uuid.UUID) ([]model.ActivityTierUpgrade, error)
	GetLatestTierUpgrade(ctx context.Context, userID uuid.UUID) (*model.ActivityTierUpgrade, error)
}

// ActivityTierRepository implements the activity tier repository interface
type ActivityTierRepository struct {
	db *gorm.DB
}

// NewActivityTierRepository creates a new activity tier repository
func NewActivityTierRepository() ActivityTierRepositoryInterface {
	return &ActivityTierRepository{
		db: global.GVA_DB,
	}
}

// GetAllTiers retrieves all activity tiers
func (r *ActivityTierRepository) GetAllTiers(ctx context.Context) ([]model.ActivityTier, error) {
	var tiers []model.ActivityTier
	err := r.db.WithContext(ctx).Order("level ASC").Find(&tiers).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get all tiers: %w", err)
	}
	return tiers, nil
}

// GetTierByID retrieves a tier by ID
func (r *ActivityTierRepository) GetTierByID(ctx context.Context, id uint) (*model.ActivityTier, error) {
	var tier model.ActivityTier
	err := r.db.WithContext(ctx).First(&tier, id).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get tier by ID: %w", err)
	}
	return &tier, nil
}

// GetTierByLevel retrieves a tier by level
func (r *ActivityTierRepository) GetTierByLevel(ctx context.Context, level int) (*model.ActivityTier, error) {
	var tier model.ActivityTier
	err := r.db.WithContext(ctx).Where("level = ?", level).First(&tier).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get tier by level: %w", err)
	}
	return &tier, nil
}

// CreateTier creates a new tier
func (r *ActivityTierRepository) CreateTier(ctx context.Context, tier *model.ActivityTier) error {
	err := r.db.WithContext(ctx).Create(tier).Error
	if err != nil {
		return fmt.Errorf("failed to create tier: %w", err)
	}
	return nil
}

// UpdateTier updates an existing tier
func (r *ActivityTierRepository) UpdateTier(ctx context.Context, tier *model.ActivityTier) error {
	err := r.db.WithContext(ctx).Save(tier).Error
	if err != nil {
		return fmt.Errorf("failed to update tier: %w", err)
	}
	return nil
}

// DeleteTier deletes a tier
func (r *ActivityTierRepository) DeleteTier(ctx context.Context, id uint) error {
	err := r.db.WithContext(ctx).Delete(&model.ActivityTier{}, id).Error
	if err != nil {
		return fmt.Errorf("failed to delete tier: %w", err)
	}
	return nil
}

// GetUserActivityLevel retrieves a user's activity level
func (r *ActivityTierRepository) GetUserActivityLevel(ctx context.Context, userID uuid.UUID) (*model.UserActivityLevel, error) {
	var level model.UserActivityLevel
	err := r.db.WithContext(ctx).
		Preload("ActivityTier").
		Preload("User").
		Where("user_id = ?", userID).
		First(&level).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user activity level: %w", err)
	}
	return &level, nil
}

// CreateUserActivityLevel creates a new user activity level
func (r *ActivityTierRepository) CreateUserActivityLevel(ctx context.Context, level *model.UserActivityLevel) error {
	err := r.db.WithContext(ctx).Create(level).Error
	if err != nil {
		return fmt.Errorf("failed to create user activity level: %w", err)
	}
	return nil
}

// UpdateUserActivityLevel updates a user's activity level
func (r *ActivityTierRepository) UpdateUserActivityLevel(ctx context.Context, level *model.UserActivityLevel) error {
	err := r.db.WithContext(ctx).Save(level).Error
	if err != nil {
		return fmt.Errorf("failed to update user activity level: %w", err)
	}
	return nil
}

// GetUsersByTier retrieves all users in a specific tier
func (r *ActivityTierRepository) GetUsersByTier(ctx context.Context, tierID uint) ([]model.UserActivityLevel, error) {
	var levels []model.UserActivityLevel
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("ActivityTier").
		Where("activity_tier_id = ?", tierID).
		Find(&levels).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get users by tier: %w", err)
	}
	return levels, nil
}

// CreatePointTransaction creates a new point transaction
func (r *ActivityTierRepository) CreatePointTransaction(ctx context.Context, transaction *model.UserPointTransaction) error {
	err := r.db.WithContext(ctx).Create(transaction).Error
	if err != nil {
		return fmt.Errorf("failed to create point transaction: %w", err)
	}
	return nil
}

// GetUserPointTransactions retrieves user's point transactions with pagination
func (r *ActivityTierRepository) GetUserPointTransactions(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.UserPointTransaction, error) {
	var transactions []model.UserPointTransaction
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&transactions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user point transactions: %w", err)
	}
	return transactions, nil
}

// GetUserPointBalance calculates user's current point balance
func (r *ActivityTierRepository) GetUserPointBalance(ctx context.Context, userID uuid.UUID) (int, error) {
	var result struct {
		Balance int
	}
	err := r.db.WithContext(ctx).
		Model(&model.UserPointTransaction{}).
		Select("COALESCE(SUM(points), 0) as balance").
		Where("user_id = ?", userID).
		Scan(&result).Error
	if err != nil {
		return 0, fmt.Errorf("failed to get user point balance: %w", err)
	}
	return result.Balance, nil
}

// GetUserPointTransactionsByType retrieves user's point transactions by type
func (r *ActivityTierRepository) GetUserPointTransactionsByType(ctx context.Context, userID uuid.UUID, transactionType string) ([]model.UserPointTransaction, error) {
	var transactions []model.UserPointTransaction
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND type = ?", userID, transactionType).
		Order("created_at DESC").
		Find(&transactions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user point transactions by type: %w", err)
	}
	return transactions, nil
}

// CreateTierUpgrade creates a new tier upgrade record
func (r *ActivityTierRepository) CreateTierUpgrade(ctx context.Context, upgrade *model.ActivityTierUpgrade) error {
	err := r.db.WithContext(ctx).Create(upgrade).Error
	if err != nil {
		return fmt.Errorf("failed to create tier upgrade: %w", err)
	}
	return nil
}

// GetUserTierUpgrades retrieves user's tier upgrade history
func (r *ActivityTierRepository) GetUserTierUpgrades(ctx context.Context, userID uuid.UUID) ([]model.ActivityTierUpgrade, error) {
	var upgrades []model.ActivityTierUpgrade
	err := r.db.WithContext(ctx).
		Preload("FromTier").
		Preload("ToTier").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&upgrades).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user tier upgrades: %w", err)
	}
	return upgrades, nil
}

// GetLatestTierUpgrade retrieves user's latest tier upgrade
func (r *ActivityTierRepository) GetLatestTierUpgrade(ctx context.Context, userID uuid.UUID) (*model.ActivityTierUpgrade, error) {
	var upgrade model.ActivityTierUpgrade
	err := r.db.WithContext(ctx).
		Preload("FromTier").
		Preload("ToTier").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		First(&upgrade).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get latest tier upgrade: %w", err)
	}
	return &upgrade, nil
}
